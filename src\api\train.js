import { BASE_URL_MEDICAL } from './config';
import axios from 'axios';

// 获取培训资料
export function queryTrainingInfo (obj) {
    const url = BASE_URL_MEDICAL + 'supervisor/queryTrainingInfo';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}

// 获取培训资料详情
export function selectTrainingInfo (obj) {
  const url = BASE_URL_MEDICAL + 'supervisor/selectTrainingInfo';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}