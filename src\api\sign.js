import { BASE_URL_MEDICAL } from './config';
import axios from 'axios';
import {initCookie} from '@/api/check'
// 获取签到附近的地点列表
export function queryOrganByMyCoordinate (obj) {
    const url = BASE_URL_MEDICAL + 'supervisor/queryOrganByMyCoordinate';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}
// 签到
export function checkIn (obj) {
    const url = BASE_URL_MEDICAL + 'check/checkIn';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}
// 首页获取用户信息
export function getUserIn (obj) {
    const url = BASE_URL_MEDICAL + 'supervisor/getUserInfo';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}
// 补充机构列表
export function queryOrganForAdd (obj) {
  const url = BASE_URL_MEDICAL + 'check/queryOrganForAdd';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}
// 机构补充/纠错申请上报
export function addRecoveryOrgan (obj) {
  const url = BASE_URL_MEDICAL + 'check/addRecoveryOrgan';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 机构列表查询
export function queryOrganByOrgName (obj) {
  const url = BASE_URL_MEDICAL + 'supervisor/queryOrganByOrgName';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

