<template>
<div class="not">
	<img src="../../assets/img/<EMAIL>" alt="">
	<p>十分抱歉，页面出错了</p>
	<div class="btn">
		<v-button type="default" @click="toIndex" size="large"><span slot="text">返回首页</span></v-button>
	</div>
</div>
</template>
<script>
import vButton from '../../components/v-button/button';
export default {
	data () {
		return {};
	},
	mounted () {
		this.$loading(false, '')
	},
	methods: {
		toIndex () {
			this.$router.replace('/')
		}
	},
	components: {
		vButton
	},
	watch: {}
};
</script>
<style lang="less" scoped>
.not{
	padding-top: 192px;
	text-align: center;
	img{
		width: 492px;
		height: 317px;
	}
	p{
		margin-top: 107px;
		margin-bottom: 136px;
		font-size: 32px;
		color: rgba(38, 38, 40, 0.4);
	}
	.btn{
		height: 98px;
		padding: 0 84px;
	}
}
</style>
