import confirmBox from './confirmBox.vue';

export default {
  install (Vue, options = {}) {
		let confirm = null;
		// 全局确认框
    Vue.prototype.$confirmBox = (boxData = {
        title: '标题',
        content: '内容',
        callback: function () {
          console.log('callback');
        },
        openCkeckSign: function () {
					console.log('openCkeckSign.');
				}
      }) => {
			const ConfirmBoxObj = Vue.extend(confirmBox);
			confirm = new ConfirmBoxObj();
			if (!confirm.$el) {
				confirm.$mount();
				document.querySelector('body').appendChild(confirm.$el);
			}
			confirm.setInfo(boxData);
      confirm.open();
    };
  }
};
