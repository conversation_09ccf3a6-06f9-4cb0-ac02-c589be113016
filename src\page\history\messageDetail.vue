<template>
    <div class="train-con">
        <div class="train">
            <h2 class="train-title">{{infoTitle}}</h2>
            <div class="train-content" >{{infoContent}}</div>            
         </div>
    </div>
</template>
<script>

export default {
    data () {
        return{
            infoContent: this.$route.query.content,
            infoTitle: this.$route.query.title
        }
    },
    mounted () {
    },
    methods: {}
}
</script>
<style lang="less" scoped>
.train-con{
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}
.train{
    padding:30px;
    .train-title{
        font-size:32px;
        color:#000;
        margin:45px 0;
        font-weight:bold;
        text-align:center;
    }
    .train-content{
        font-size:30px;
        color:#333;
        line-height:40px;
    }
}
</style>
