<template>
  <div ref="wrapper" class="wrapper">
    <!-- //此处放加载异步加载的内容 -->
    <slot></slot>
		<div class="pullDown" ref="pullDown">
			<img :src="loadingImg" alt="">
		</div>
  </div>
</template>
<script type="text/ecmascript-6">
import BScroll from 'better-scroll';
export default {
	name: 'scroll',
	data () {
		return {
			scroll: null,
			loadingImg:require('@/assets/img/loading-80.gif')
		};
	},
	props: {
    probeType: {
      type: Number,
      default: 0
		},
    click: {
      type: Boolean,
      default: true
		},
    pullUpLoad: {
      type: Boolean,
      default: true
		},
    pullDownRefresh: {
      type: Boolean,
      default: true
    },
    data: {
      type: Array,
      default: null
		}
	},
	mounted () {
		setTimeout(() => {
			this.init();
		}, 100);
	},
	methods: {
		init () {
			const _this = this;
			// 初始化
      _this.scroll = new BScroll(this.$refs.wrapper, {
        probeType: _this.probeType,
				click: _this.click,
				pullDownRefresh: _this.pullDownRefresh,
				pullUpLoad: _this.pullUpLoad,
				momentum: true
			});
			// 上拉刷新
			_this.scroll.on('touchEnd', function (ev) {
				if (ev.y > 50) {
					// console.log('下拉刷新');
					// 传参_this.scroll，主要是下拉刷新成功后调finishPullDown;
					_this.$emit('pullDownFresh', _this.scroll);
				} else if (ev.y > 0 && ev.y <= 50) {
					// console.log('_loadingBack');
					_this._loadingHide();
				}
			});
			// 下拉加载
			_this.scroll.on('scrollEnd', (pos) => {
				if (_this.scroll.y <= _this.scroll.maxScrollY + 50 && _this.scroll.y >= _this.scroll.maxScrollY) {
					// 当上拉加载数据加载完毕后，需要执行 finishPullUp 方法
					_this.$emit('scrollToEnd', _this.scroll);
				}
			});
			// 滚动事件
			_this.scroll.on('scroll', (pos) => {
				_this.$emit('scroll', pos);
				if (pos.y > 0 && pos.y < 50) {
					this.$refs.pullDown.style.cssText = `top:${-50 + pos.y}px;`;
				}
			});
		},
    refresh () {
      this.scroll && this.scroll.refresh();
		},
    enable () {
      this.scroll && this.scroll.enable();
    },
    disable () {
      this.scroll && this.scroll.disable();
		},
		// 隐藏下拉刷新loading
		_loadingHide () {
			const _this = this;
			let pullDownPosition = parseFloat(_this._getStyle(this.$refs.pullDown, 'top'));
			let intervalID = null;
			intervalID = setInterval(() => {
				if (pullDownPosition < 2 && pullDownPosition > -50) {
					_this.$refs.pullDown.style.cssText = `top:${pullDownPosition - 1}px;`;
					pullDownPosition = parseFloat(_this._getStyle(this.$refs.pullDown, 'top'));
				} else {
					clearInterval(intervalID);
					_this.$refs.pullDown.style.cssText = `top:-50px;`;
				}
			}, 10);
		},
		_getStyle (obj, attr) {
			if (obj.currentStyle) {
				return obj.currentStyle[attr];
			} else {
				return getComputedStyle(obj, false)[attr];
			}
		}
	},
	components: {},
	watch: {
		pullUpLoad (n, o) {
			if (n) {
				this.enable();
				this.refresh();
			} else {
				this.disable();
			}
		},
		pullDownRefresh (n, o) {
			if (n) {
				this.enable();
				this.refresh();
				this._loadingHide();
			} else {
				this.disable();
			}
		}
	}
};
</script>
<style lang="less" scoped>
.wrapper{
	height: 100%;
	.pullDown{
		position: absolute;
		top: -50px;/*no*/
		left: 0;
		width: 100%;
		height: 50px;/*no*/
		line-height: 50px;/*no*/
		text-align: center;
		img{
			height: 30px;/*no*/
			vertical-align: middle;
		}
	}
}
</style>
