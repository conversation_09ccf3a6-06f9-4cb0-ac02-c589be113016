// 检验字符是否为空
export function isEmpty (obj) {
    if (typeof obj === 'undefined' || obj === null || obj === '') {
        return true
    } else {
        return false
    }
}

// 检验手机号是否正确
export function testPhone (phone) {
    let regPhone = /^[1][0-9]{10}$/ // 手机号码正则
    if (regPhone.test(phone)) {
      return true
    } else {
      return false
    }
}

export default {  
    _debounce(fn, delay) {    
    var delay = delay || 200; 
    var timer;
    return function () {
        var th = this;
        var args = arguments;
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(function () {
            timer = null;
            fn.apply(th, args);}, delay);
        };
    },  
    // 节流  
    _throttle(fn, interval) {
        var last;
        var timer;
        var interval = interval || 200;
        return function () {
            var th = this;
            var args = arguments;
            var now = +new Date();
            if (last && now - last < interval) {
                clearTimeout(timer);
                timer = setTimeout(function () {
                last = now;
                fn.apply(th, args);}, interval);
            } else {
                last = now;
                fn.apply(th, args);
            }
        }
    }
}


export function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

export function getParam(paramName) {
    paramValue = "", isFound = !1;
    if (this.location.search.indexOf("?") == 0 && this.location.search.indexOf("=") > 1) {
        arrSource = unescape(this.location.search).substring(1, this.location.search.length).split("&"), i = 0;
        while (i < arrSource.length && !isFound) arrSource[i].indexOf("=") > 0 && arrSource[i].split("=")[0].toLowerCase() == paramName.toLowerCase() && (paramValue = arrSource[i].split("=")[1], isFound = !0), i++
    }
    return paramValue == "" && (paramValue = null), paramValue
}

// 新增：从URL获取参数的通用方法
export function getUrlParam(paramName) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(paramName);
}

// 新增：获取用户信息相关参数
export function getUserInfoFromUrl() {
    return {
        longitude: getUrlParam('longitude') || getUrlParam('lng'),
        latitude: getUrlParam('latitude') || getUrlParam('lat'),
        authToken: getUrlParam('authToken') || getUrlParam('token') || getUrlParam('authCode'),
        origin: getUrlParam('origin') || getUrlParam('channelId'),
        userId: getUrlParam('userId') || getUrlParam('uid'),
        userName: getUrlParam('userName') || getUrlParam('name')
    };
}

// 新增：合并用户信息的工具函数
export function mergeUser(apiUserInfo) {
    // 根据API返回的用户信息格式进行处理
    return {
        userId: apiUserInfo.userId || apiUserInfo.id,
        userName: apiUserInfo.userName || apiUserInfo.name || apiUserInfo.realName,
        phone: apiUserInfo.phone || apiUserInfo.mobile,
        idCard: apiUserInfo.idCard || apiUserInfo.idNumber,
        orgName: apiUserInfo.orgName || apiUserInfo.organizationName,
        // 添加其他需要的字段
        ...apiUserInfo
    };
}

// 新增：检查Token是否有效
export function checkToken(token) {
    if (!token) return false;
    // 简单的token格式验证，可以根据实际需求调整
    return token.length > 10;
}

// 新增：保存用户信息到localStorage
export function saveUserInfo(userInfo) {
    try {
        if (userInfo.authToken) {
            window.localStorage.setItem("authToken", userInfo.authToken);
        }
        if (userInfo.longitude && userInfo.latitude) {
            window.localStorage.setItem("userLocation", JSON.stringify({
                longitude: userInfo.longitude,
                latitude: userInfo.latitude
            }));
        }
        if (userInfo.userId) {
            window.localStorage.setItem("userId", userInfo.userId);
        }
        if (userInfo.userName) {
            window.localStorage.setItem("userName", userInfo.userName);
        }
        return true;
    } catch (e) {
        console.error('保存用户信息失败:', e);
        return false;
    }
}

// 新增：从localStorage获取用户信息
export function getUserInfoFromStorage() {
    try {
        const authToken = window.localStorage.getItem("authToken");
        const locationStr = window.localStorage.getItem("userLocation");
        const userId = window.localStorage.getItem("userId");
        const userName = window.localStorage.getItem("userName");

        let location = null;
        if (locationStr) {
            location = JSON.parse(locationStr);
        }

        return {
            authToken,
            longitude: location ? location.longitude : null,
            latitude: location ? location.latitude : null,
            userId,
            userName
        };
    } catch (e) {
        console.error('获取用户信息失败:', e);
        return {};
    }
}

export function getOptions(type) {
    if(type === 'busiStatus'){
        return [
            {
                key:'1',
                value:'正常'
            },
            {
                key:'2',
                value:'暂停'
            },
            {
                key:'3',
                value:'缓签'
            },
            {
                key:'4',
                value:'解除'
            },
            {
                key:'5',
                value:'取消'
            }
        ]
    } 
    if(type === 'orgLevel'){
        return [
            {
                key:'1',
                value:'1级'
            },
            {
                key:'2',
                value:'2级'
            },
            {
                key:'3',
                value:'3级'
            },
            {
                key:'4',
                value:'4级'
            }
        ]
    }
    return []
}

// 判断是否在市民卡App
export function isSMKApp() {
	var u = navigator.userAgent
	var reg = RegExp(/AppleWebKit/gi);
	var reg1 = RegExp(/smkVersion/gi);
	return reg.test(u) && reg1.test(u)
}

// AES加密工具函数
// 使用CryptoJS库进行AES-CBC加密
import CryptoJS from 'crypto-js';

export function aesEncrypt(data, key, iv) {
    // 如果没有传入key和iv，使用默认值（与后端保持一致）
    const defaultKey = key || '1235567890023656'; // 16位密钥
    const defaultIv = iv || '1233568890123888';   // 16位IV

    // 验证密钥和IV长度
    if (defaultKey.length !== 16) {
        throw new Error(`密钥长度必须为16位，当前为${defaultKey.length}位`);
    }
    if (defaultIv.length !== 16) {
        throw new Error(`IV长度必须为16位，当前为${defaultIv.length}位`);
    }

    try {
        // 数据预处理和验证
        let dataToEncrypt;
        if (typeof data === 'string') {
            dataToEncrypt = data;
        } else if (data === null || data === undefined) {
            throw new Error('加密数据不能为空');
        } else {
            dataToEncrypt = JSON.stringify(data);
        }

        // 验证数据是否为空或过长
        if (!dataToEncrypt || dataToEncrypt.length === 0) {
            throw new Error('加密数据不能为空字符串');
        }

        // 限制数据长度，避免内存问题
        if (dataToEncrypt.length > 10000) {
            throw new Error('加密数据过长，超过10000字符限制');
        }

        console.log('加密参数:', {
            data: data,
            dataToEncrypt: dataToEncrypt,
            dataLength: dataToEncrypt.length,
            key: defaultKey,
            iv: defaultIv
        });

        // 使用CryptoJS进行AES-CBC加密
        const encrypted = CryptoJS.AES.encrypt(dataToEncrypt, CryptoJS.enc.Utf8.parse(defaultKey), {
            iv: CryptoJS.enc.Utf8.parse(defaultIv),
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });

        // 验证加密结果
        if (!encrypted || !encrypted.toString()) {
            throw new Error('加密结果为空');
        }

        // 返回Hex格式的加密结果（与工具一致）
        const result = encrypted.ciphertext.toString(CryptoJS.enc.Hex);
        console.log('加密成功，结果长度:', result.length);
        return result;

    } catch (error) {
        console.error('AES加密失败:', error);
        console.error('加密参数详情:', {
            data: data,
            dataType: typeof data,
            key: defaultKey,
            keyLength: defaultKey.length,
            iv: defaultIv,
            ivLength: defaultIv.length
        });

        // 重新抛出更详细的错误信息
        throw new Error(`AES加密失败: ${error.message}`);
    }
}

// 使用示例：
// const data = { userId: '123' };
// const encrypted = aesEncrypt(data);
// getUserInfoByEnc({ encoder: encrypted });

// 从localStorage读取用户信息
export function getStoredUserInfo() {
    try {
        const userInfoStr = window.localStorage.getItem('userInfo');
        return userInfoStr ? JSON.parse(userInfoStr) : {};
    } catch (e) {
        console.error('读取本地用户信息失败:', e);
        return {};
    }
}

// 从localStorage读取位置信息，若无则给默认杭州坐标
export function getStoredUserLocation() {
    try {
        const locationStr = window.localStorage.getItem('userLocation');
        if (locationStr) {
            const parsed = JSON.parse(locationStr);
            if (
                parsed &&
                typeof parsed.longitude === 'number' &&
                typeof parsed.latitude === 'number'
            ) {
                return {
                    success: true,
                    source: 'storage',
                    location: parsed
                };
            }
        }
    } catch (e) {
        console.error('读取本地位置信息失败:', e);
    }
    return {
        success: true,
        source: 'default',
        location: {
            longitude: 120.21,
            latitude: 30.26
        }
    };
}