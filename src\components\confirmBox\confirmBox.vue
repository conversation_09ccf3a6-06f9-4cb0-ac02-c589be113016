<template>
  <div class="messageBoxWrap" v-if="boxStatus">
    <div class="mask"></div>
    <div class="messageBox">
      <div class="messageBoxTitle">
        <h1>{{boxData.title}}</h1>
      </div>
      <div class="messageBoxContent">
        <p>{{boxData.content}}</p>
      </div>
      <div class="messageBoxButton clearfix">
        <button type="button" class="lt" @click="openSign">忘记签字了</button>
        <button type="button" class="rt" @click="deleteConfirmOK">确认提交</button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
			boxStatus: false,
			boxData: {title: '标题', content: '内容', callback: function () { console.log('执行删除操作'); }}
    };
  },
	methods: {
		open () {
			this.boxStatus = true;
		},
		close () {
			this.boxStatus = false;
		},
		setInfo (item) {
			this.boxData = item;
		},
		openSign (){
			// console.log('sign');
			this.boxData.openCkeckSign();
			this.close();
		},
		deleteConfirmOK () {
			this.boxData.callback();
			this.close();
		}
	}
};
</script>
<style lang="less" scoped>
.messageBoxWrap{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: transparent;
	z-index: 110;
	line-height: 1.5;
}
.mask{
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.7;
}
.messageBox{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  width: 624px;
  min-height:200px;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
}
.messageBoxTitle h1{
  text-align: center;  
  font-size: 38px;
  color: #333;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
	text-overflow: ellipsis;
	padding: 51px 0 12px 0;
}
.messageBoxContent{
	padding-bottom: 54px;
}
.messageBoxContent p{
	width: 540px;
	margin: 0 auto;
  font-size: 28px;
  color: #333;
}
.messageBoxButton{
  height: 88px;
	button{
		font-size: 32px;
		font-weight: 500;
		color: #333333;
		width: 50%;
		height: 100%;
		outline: 1px solid #ececec;
		border-radius: 4px;
		float: left;
		border: none;
		background: #fff;
	}
	button.rt{
		color: #317DFF;
	}
}
</style>
