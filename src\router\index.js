import Vue from 'vue'
import Router from 'vue-router'
const helloWorld = () => import('@/components/HelloWorld')
const index = () => import('@/page/index/index')
// demo
const demo = () => import('@/page/index/demo1')
// const demo = () => import('@/components/scroll/demo.vue')
const Check = () => import('@/page/check/check')
const historyCheck = () => import('@/page/history/historyRecordCheck')
const historyRecord = () => import('@/page/history/historyRecord')
const historyMessage = () => import('@/page/history/historyMessage')
const messageDetail = () => import('@/page/history/messageDetail')
const train = () => import('@/page/train/train')
const showPdf = () => import('@/page/train/showPdf')
const checkSign = () => import('@/page/check/checkSign')
const locationCorrect = () => import('@/page/locationCorrect/locationCorrect')
const updatePosition = () => import('@/page/updatePosition/updatePosition')
const searchLocation = () => import('@/page/searchLocation/searchLocation')
// identity
const authentication = () => import('@/page/identity/authentication')
const identityCheck = () => import('@/page/identity/identityCheck')
const identityInfo = () => import('@/page/identity/identityInfo')
// checkSign
const search = () => import('@/page/search/search')
// Sign
const sign = () => import('@/page/sign/sign')
const searchOrg = () => import('@/page/sign/searchOrg')
const NotFound = () => import('@/page/notFound/404.vue')
Vue.use(Router)
const router = new Router({
    routes: [
        /* {
          path: '/',
          name: 'Search',
          redirect: '/search',
          meta: {
            title: '医保监督'
          }
        }, */
        {
            path: '/',
            name: 'Index',
            redirect: '/index',
            meta: {
                title: '医保监督'
            }
        },
        {
            path: '/index',
            name: 'Index',
            component: index,
            meta: {
                title: '医保监督'
            }
        },
        {
            path: '/demo',
            name: 'Demo',
            component: demo,
            meta: {
                title: '例子'
            }
        },
        {
            path: '/historyRecord',
            name: 'HistoryRecord',
            component: historyRecord,
            meta: {
                title: '历史纪录'
            }
        },
        {
            path: '/historyCheck',
            name: 'historyCheck',
            component: historyCheck,
            meta: {
                title: '历史纪录'
            }
        },
        {
            path: '/historyMessage',
            name: 'HistoryMessage',
            component: historyMessage,
            meta: {
                title: '消息通知'
            }
        },
        {
            path: '/messageDetail',
            name: 'MessageDetail',
            component: messageDetail,
            meta: {
                title: '消息通知'
            }
        },
        {
            path: '/train',
            name: 'Train',
            component: train,
            meta: {
                title: '培训材料'
            }
        },
        {
            path: '/check',
            name: 'Check',
            component: Check,
            meta: {
                title: '检查结果录入'
            }
        },
        {
            path: '/checkSign',
            name: 'checkSign',
            component: checkSign,
            meta: {
                title: '签名'
            }
        },
        {
            path: '/authentication',
            name: 'Authentication',
            component: authentication,
            meta: {
                title: '身份验证'
            }
        },
        {
            path: '/identityCheck',
            name: 'IdentityCheck',
            component: identityCheck,
            meta: {
                title: '身份校验'
            }
        },
        {
            path: '/identityInfo',
            name: 'identityInfo',
            component: identityInfo,
            meta: {
                title: '身份信息'
            }
        },
        {
            path: '/search',
            name: 'search',
            component: search,
            meta: {
                title: '机构查询'
            }
        },
        {
            path: '/sign',
            name: 'Sign',
            component: sign,
            meta: {
                title: '签到'
            }
        },
        {
            path: '/locationCorrect/:id',
            name: 'locationCorrect',
            component: locationCorrect,
            meta: {
                title: '地址纠错'
            }
        },
        {
            path: '/updatePosition/:id',
            name: 'updatePosition',
            component: updatePosition,
            meta: {
                title: '定位地址'
            }
        },
        {
            path: '/searchLocation',
            name: 'searchLocation',
            component: searchLocation,
            meta: {
                title: '定位地址'
            }
        },
        {
            path: '/searchOrg',
            name: 'searchOrg',
            component: searchOrg,
            meta: {
                title: '签到机构'
            }
        },
        {
            path: '/showPdf',
            name: 'showPdf',
            component: showPdf,
            meta: {
                title: ''
            }
        },
        {
            path: '*',
            name: 'NotFound',
            component: NotFound,
            meta: {
                title: '404'
            }
        }
    ]
})
// 添加全局更改路由title的方法
router.beforeEach((to, from, next) => {
    // 路由发生变化更改title
    if (to.name === 'locationCorrect') {
        to.meta.title = to.params.id === '2' ? '地址补充' : '地址纠错'
    }
    if (to.meta.title) {
        document.title = to.meta.title
    }
    next()
})

export default router