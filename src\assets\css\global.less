.amap-logo {
  display: none !important;
}
.amap-copyright{
	visibility:hidden !important;
}
.mapSC {
  position: fixed;
	bottom: 0;
	width: 100%;
	min-height: 316px;
  left: 0;
	background: #fff;
	.mapSCp{
		padding: 42px 30px;
		h2{
			font-size: 34px;
			color: #000;
			font-weight: bold;
			margin-bottom: 11px;
			width:570px; 
			.orgName {
			   margin-right: 20px;
			}
			.noright {
				margin-right: 0;
			}
			span{
				// display: inline-block;
				// font-size: 26px;
				// margin: 0 0 0 20px;
				// padding: 2px 16px;
				// font-weight: 500;
				// border-radius: 6px;
				// height: 34px;
				// line-height: 34px;
			}
			.orgLevel {
				display: inline-block;
				font-size: 26px;
				padding: 0px 16px;
				font-weight: 500;
				border-radius: 6px;
				// height: 34px;
				// line-height: 34px;
				color: #2CD897;
				border: 2px solid #2CD897;
				box-sizing: border-box;
			}
			.busiStatus {
				display: inline-block;
				font-size: 26px;
				margin-right: 20px;
				padding: 0px 16px;
				font-weight: 500;
				border-radius: 6px;
				// height: 34px;
				// line-height: 34px;
				color: #333333;
				border: 2px solid #333333;
				box-sizing: border-box;
			}
		}
		.p1{
			font-size: 28px;
			color: #333;
			font-weight: 500;
			margin-bottom: 7px;
			width:570px;
		}
		.p2 {
			font-size: 28px;
			color: #999;
			font-weight: 500;
			margin-bottom: 33px;
			span{
				display: inline-block;
				padding: 0 15px 0 22px;
			}
		}
		.p3{
			font-size: 28px;
			color: #777;
			font-weight: 500;
		}
	}
	.mapSCs{
		position: absolute;
		right: 20px;
		top: -60px;
		width: 120px;
		height: 120px;
		.mapSCs1 {
			img{
				width: 100%;
			}
		}
		.mapSCs2{
			position: absolute;
			bottom: 120px;
			overflow: hidden;
			height: 0;
			.img{
				box-sizing: border-box;
				width: 120px;
				height: 120px;
				border-radius: 50%;
				margin-bottom: 20px;
				background: #fff;
				position: relative;
				box-shadow: 0px 3px 13px 0px rgba(0, 0, 0, 0.19);
				img{
					position: absolute;
					top: 0;
					bottom: 0;
					left: 0;
					right: 0;
					margin: auto;
				}
			}
			.img1{
				img {
					width: 54px;
				}
			}
			.img2 {
			  img {
			    width: 54px;
			  }
			}
			.img3 {
			  img {
			    width: 38px;
			  }
			}
		}
		.mapSCs2act{
			height: auto;
		}
	}
}


.searchbar {
    background-color: #F6F6F7;
    padding: 0 30px;
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    width: calc(100% - 60px);
	height: 120px;
	.searchbar-inner {
		background: white;
		display: flex;
		flex: 1;
		align-items: center;
		border-radius: 44px;
		padding: 14px 0px 14px 15px;
	}
	.searchbar-inner-location {
		color:#333333;
		font-size: 32px;
		flex: 0 0 80px;
		border-right: 2px solid #E4E3E3;
		margin-left: 20px;
		margin-right: 10px;
		
	}
	
	.search-input-con {
		flex: 1;
		display: flex;
		align-items: center;
	}
	.search-input {
		color: #777777;
		font-size: 30px;
		box-sizing: border-box;
		width: 100%;
		height: 100%;
		border: none;
		// border-radius: 4px;
		padding: 10px;
		border-radius: 44px;
		&:focus {
			outline: none;
		}
	}
	.search-input-icon {
		width: 32px;
		height: 33px;
		background-image: url('~@/assets/img/<EMAIL>');
		background-repeat: no-repeat;
		background-size: 100%;
		margin-left: 10px;
	}
	.search-input-icon-right {
		width: 32px;
		height: 32px;
		background: url('~@/assets/img/<EMAIL>') no-repeat center center;
		background-size: 32px 32px;
		padding-right: 10px;
		margin-right: 20px;
	}
	
}


input[type=search] {
	-webkit-appearance: textfield;
	-webkit-box-sizing: content-box;
	font-family: inherit;
	font-size: 100%;
}
input::-webkit-search-decoration,
input::-webkit-search-cancel-button {
	display: none;
}

