<template>
<div class="scroll-container">
	<Scroll 
		:probeType="probeType"
		:data="listData"
		:pullDownRefresh="pullDownRefresh"
		:pullUpLoad="pullUpLoad"
		@pullDownFresh="pullDown"
		@scroll="scrollPosition"
		@scrollToEnd="scrollToEndFn"
	>
		<ul class="content">
			<li v-for="(item, index) in listData" :key="index">{{item}}</li>
			<div class="pullUpLoad">
				<img :src="loadingImg" v-if="pullUpLoad"/>
				<span v-else>下拉加载更多</span>
			</div>
		</ul>
	</Scroll>
</div>
</template>
<script>
import Scroll from './scroll'
export default {
	data () {
		return {
			probeType: 2,
			listData: [],
			pullDownRefresh: true,	// 控制能否刷新
			pullUpLoad: true,	// 控制能否加载
			loadingImg:require('@/assets/img/loading-80.gif')
		};
	},
	mounted () {
		this.getData()
	},
	methods: {
		getData () {
			this.listData =[]
			if (this.pullDownRefresh) {
				this.pullDownRefresh = false
				let arr =[];
				for (let i = 0; i < 15; i++) {
					arr.push(i)
				}
				setTimeout(() => {
					this.listData = this.listData.concat(arr)
					this.pullDownRefresh = true
				}, 2000);
			}
		},
		// 下拉刷新
		pullDown () {
			this.getData()
		},
		// 获取滚动的坐标
		scrollPosition (pos) {
		},
		// 上拉加载
		scrollToEndFn () {
			console.log('scrollToEnd');
			const _this = this;
			if (_this.pullUpLoad) {
				_this.pullUpLoad = false
				let arr =[];
				for (let i = 0; i < 5; i++) {
					arr.push(i)
				}
				setTimeout(() => {
					this.listData = this.listData.concat(arr)
					this.pullUpLoad = true
				}, 2000);
			}
		}
	},
	components: {
		Scroll
	},
	watch: {}
};
</script>
<style lang="less" scoped>
.scroll-container{
	position: relative;
	height: 100%;
	overflow: auto;
	.content{
		li{
			padding: 20px 0;
			border-bottom: 1px solid #ddd;
		}
		.pullUpLoad{
			height: 50px;
			line-height: 50px;
			text-align: center;
			img{
				height: 30px;/*no*/
				vertical-align: middle;
			}
		}
	}
}
</style>
