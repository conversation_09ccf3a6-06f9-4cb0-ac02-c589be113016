// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import MintUI from 'mint-ui'
import 'mint-ui/lib/style.css'
// import 'lib-flexible/flexible.js'
import '@/assets/js/flexible.js'
import '@/assets/reset.css'
import store from './store/index';
Vue.config.productionTip = false
Vue.use(MintUI)
// import vConsole from 'vconsole'
// new vConsole()
// 全局样式
import './assets/css/global.less'
// loading
// this.$loading(true, 'loading...');
import Loading from '@/components/loading/loading.js';
Vue.use(Loading);
console.log('process.env.NODE_ENV==', process.env.NODE_ENV);
import confirmBox from '@/components/confirmBox/confirmBox.js';
Vue.use(confirmBox);
// this.$result(false)
// true ：成功
// false ：失败
import result from '@/components/v-toast/toast.js';
Vue.use(result)
// this.$toast.center('top');
import '@/components/toast/toast.less'
import toast from '@/components/toast/toast.js';
Vue.use(toast)
import iNoBounce from 'inobounce';
Vue.use(iNoBounce)
/* eslint-disable no-new */
new Vue({
  el: '#app',
	router,
	store,
  components: { App },
  template: '<App/>'
});
