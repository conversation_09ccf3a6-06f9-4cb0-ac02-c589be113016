<template>
    <div class="historyRecord">
        <div class="tab">
            <div class="tab-left" @click="toggle">
                <p class="tab-title" :class="leftShow ? 'tab-active': ''">检查记录</p>
                <i :class="leftShow ? 'bottom-line': ''"></i>
            </div>
            <div class="tab-right" @click="toggle">
                <p class="tab-title" :class="rightShow ? 'tab-active': ''">草稿箱</p>
                <i :class="rightShow ? 'bottom-line': ''"></i>
            </div>
        </div>
        <div v-if="leftShow" class="left">
            <ul class="mui-table-view" v-show="leftShow" v-if="list.length !== 0" v-infinite-scroll="loadMore" infinite-scroll-disabled="moreLoading" infinite-scroll-distance="10" infinite-scroll-immediate-check="false">
                <li class="mui-table-view-cell" >
                    <div class="tab-container-left"  v-for="item in list" :key="item.id" >
                        <div class="tab-left-title">{{item.date}}</div>
                        <div class="tab-left-detail" @click="pageTo('historyCheck', item2.checkId)" v-for="item2 in item.list" :key="item2.checkId">
                            <div class="tab-detail-con">
                                <div class="tab-detail-content">{{item2.orgName}}</div>
                                <div class="tab-detail-btn" v-show="item2.orgConfirm === null || item2.orgConfirm === ''">未确认</div>
                                <img src="../../assets/img/leftArrow.png" alt="" class="tab-detail-arrow" :class="item2.orgConfirm !== null ? 'confirm-detail-arrow':''">
                            </div>
                        </div>
                    </div>
                </li>
                <!--底部判断是加载图标还是提示“全部加载”-->
                <li class="more_loading" v-show="!queryLoading">
                    <mt-spinner type="snake" color="#ececec" :size="20" v-show="moreLoading&&!allLoaded"></mt-spinner>
                    <span v-show="allLoaded">已全部加载</span>
                </li>
            </ul>
            <div class="no-data" v-else>
                <img src="../../assets/img/nodata.png" alt="">
                <p>暂无检查记录</p>
            </div>
        </div>
        <div v-if="rightShow" class="right">
            <ul class="mui-table-view" v-show="rightShow"  v-if="list.length !== 0" v-infinite-scroll="loadMore2" infinite-scroll-disabled="moreLoading" infinite-scroll-distance="10" infinite-scroll-immediate-check="false">
                <li class="mui-table-view-cell" >
                     <div class="tab-container-right"  v-for="(item,index) in list" :key="index" v-if="list.length !== 0">
                        <div>
                            <div class="tab-left-title">{{item.date}}</div>
                            <div class="tab-left-detail" @click="pageTo('check', item2.checkId, 'drafts')" v-for="item2 in item.list" :key="item2.checkId">
                                <div class="tab-detail-con">
                                    <div class="tab-detail-content">{{item2.orgName}}</div>
                                    <div class="tab-detail-btn" v-show="item2.orgConfirm === null || item2.orgConfirm === ''">未确认</div>
                                    <img src="../../assets/img/leftArrow.png" alt="" class="tab-detail-arrow" :class="item2.orgConfirm !== null ? 'confirm-detail-arrow':''">
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li class="more_loading" v-show="!queryLoading">
                    <mt-spinner type="snake" color="#ececec" :size="20" v-show="moreLoading&&!allLoaded"></mt-spinner>
                    <span v-show="allLoaded">已全部加载</span>
                </li>
            </ul>
            <div class="no-data" v-else>
                <img src="../../assets/img/nodata.png" alt="">
                <p>暂无检查记录</p>
            </div>
        </div>
    </div>
</template>
<script>
import { queryHistoryChecks, queryCheckDetailList, queryHistoryChecksByDate } from '@/api/history'
export default {
    data () {
        return {
            leftShow: true,
            rightShow: false,
            status: false,
            list: [],
            queryLoading: false,
            moreLoading: false,
            allLoaded: false,
            pageSize: 20,
            pageNum: 0,
            rightPageNum: 0
        }
    },
    mounted () {
        console.log(this.$router)
        console.log(999, this.$route.query.type, this.$route.query.type === 'drafts')
        this.$loading(true, '');
        if (this.$route.query.type === 'drafts') {
            console.log(1111, 'drafts')
            this.leftShow = false
            this.rightShow = true
            this.rightPageNum = 0
            this.loadMore2()
        } else {
            this.pageNum = 0
            this.loadMore()
        }
    },
    beforeRouteLeave (to, from, next) {
        console.log(to)
        console.log(from)
        if(to.name === 'IdentityCheck') {
            next('/index')
        } else {
            next()
        }
    },
    methods: {
        pageTo (url, checkId, type) {
            this.$router.push({
                path: url,
                query: {
                    type: type,
                    checkId: checkId
                }
            })
        },
        //无限加载函数
        loadMore() { // 检查完
            if(this.allLoaded){
                this.moreLoading = true;
                return;
            }
            if(this.queryLoading){
                return;
            }
            this.moreLoading = !this.queryLoading;
            this.pageNum++;
            queryHistoryChecksByDate({
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                checkStatus: 2
            }).then((res)=>{
                if(res.errorCode === '0') {
                    this.list = this.list.concat(res.data);
                    this.allLoaded = res.data.length == 0;
                    this.$loading(false, '');
                }
                this.moreLoading = this.allLoaded;
            })
        },
         //无限加载函数
        loadMore2() { // 草稿箱
            if(this.allLoaded){
                this.moreLoading = true;
                return;
            }
            if(this.queryLoading){
                return;
            }
            this.moreLoading = !this.queryLoading;
            this.rightPageNum++;
            console.log(10, this.rightPageNum)
            queryHistoryChecksByDate({
                pageNum: this.rightPageNum,
                pageSize: this.pageSize,
                checkStatus: 1
            }).then((res)=>{
                if(res.errorCode === '0') {
                    this.list = this.list.concat(res.data);
                    this.allLoaded = res.data.length == 0;
                    this.$loading(false, '');
                }
                this.moreLoading = this.allLoaded;
            })
        },
        toggle () {
            this.leftShow = !this.leftShow
            this.rightShow = !this.rightShow
            if(this.rightShow || !this.leftShow) {
                console.log('jinlaile')
                this.list = []
                this.rightPageNum = 0
                this.allLoaded = false
                this.moreLoading = false
                this.loadMore2()
            } else {
                this.list = []
                this.pageNum = 0
                this.allLoaded = false
                this.moreLoading = false
                this.loadMore()
            }
        }
    }
}
</script>
<style lang="less" scoped>
.historyRecord{
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}
.tab{
    position:fixed;
    top:0;
    width:100%;
    height:101px;
    display:flex;
    justify-content: space-around;
    text-align:center;
    background:#fff;
    .tab-title{
        color:#333;
        font-size:32px;
        width:150px;
        margin:34px 0 10px 0;
    }
    .tab-active{
        color:#317DFF;
    }
    .bottom-line{
        height:4px;
        width:120px;
        display:inline-block;
        background-color:#317DFF;
    }
}
.left,.right{
    margin-top:100px;
}
.tab-container-left{
    // height:88px;
    line-height:80px;
    background:rgba(124,124,124,0.05);
    color:#777777;
    font-size:30px;
    .tab-left-title{
        padding-left:27px;
    }
    
}
.no-data{
    width:487px;
    height:416px;
    margin:299px auto 0 auto;
    background:#fff;
    img{
        width:100%;
        height:100%;
    }
    p{
        text-align:center;
        color:rgba(38,38,40,.4);
        font-size:32px;
        margin-top:18px;
    }
}
.tab-container-right{
    // height:88px;
    line-height:88px;
    background:rgba(124,124,124,0.05);
    color:#777777;
    font-size:30px;
    .tab-left-title{
        padding-left:27px;
    }
}
.tab-left-detail{
   background:#fff;
    border-bottom:1px solid #ececec;
    // border-top:1px solid #ececec;
    .tab-detail-con{
        display:flex;
        align-items: center;
        height:98px;
        .tab-detail-content{
            width:534px;
            color:#333;
            font-size:32px;
            padding-left:27px;
            overflow: hidden; /*自动隐藏文字*/
            text-overflow: ellipsis;/*文字隐藏后添加省略号*/
            white-space: nowrap;/*强制不换行*/
        }
    .tab-detail-arrow{
        width:17px;
        height:30px;
        margin-left:28px;
    }
    .tab-detail-btn{
        width:96px;
        height:42px;
        line-height:42px;
        font-size:24px;
        border-radius:4px;
        background:#fa832c;
        color:#fff;
        text-align:center;
        margin-left:19px;
    }
    .confirm-detail-arrow{
        margin-left:144px;
    }
}
}
.more_loading{
    display:flex;
    justify-content: center;
    margin-top:30px;
}

</style>
