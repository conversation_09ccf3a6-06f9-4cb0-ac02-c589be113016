<template>
    <div class="c">
        <h1 class="h1-1">检查结果</h1>
        <div class="li" v-for="(item, index) in checkList" :key="index">
            <div
                class="c1"
                :class="parseInt(item.checkStatus) === 1 ? 'c1Act' : ''"
            >
                <h2>{{ index + 1 }}.{{ item.checkTip }}</h2>
                <ul class="c1ul">
                    <li>
                        <span
                            class="label"
                            :class="
                                parseInt(item.checkStatus) === 1 ? 'act' : ''
                            "
                            >是</span
                        >
                    </li>
                    <li>
                        <span
                            class="label"
                            :class="
                                parseInt(item.checkStatus) === 0 ? 'act' : ''
                            "
                            >否</span
                        >
                    </li>
                </ul>
            </div>
            <div
                class="c2"
                v-show="
                    item.checkPhotos && JSON.parse(item.checkPhotos).length > 0
                "
            >
                <ul>
                    <li
                        v-for="(data, index) in item.checkPhotos &&
                        JSON.parse(item.checkPhotos)"
                        :key="'i' + index"
                    >
                        <template v-if="data.imgId && data.imgId > 0">
                            <img class="i1" :src="data.imgUrl" />
                            <div
                                class="i1-play"
                                @click="scanVideo(data.videoUrl, data.imgUrl)"
                            ></div>
                        </template>
                        <img
                            class="i1"
                            :src="data.fileUrl"
                            alt=""
                            @click="imgDetail($event)"
                            v-else
                        />
                    </li>
                </ul>
            </div>
        </div>
        <h1 class="h1-2">备注信息</h1>
        <ul class="h1-2v">
            <li
                v-for="(data, index) in check.commentPhotos &&
                JSON.parse(check.commentPhotos)"
                :key="'i' + index"
            >
                <template v-if="data.imgId && data.imgId > 0">
                    <img class="i1" :src="data.imgUrl" />
                    <div
                        class="i1-play"
                        @click="scanVideo(data.videoUrl, data.imgUrl)"
                    ></div>
                </template>
                <img
                    class="i1"
                    :src="data.fileUrl"
                    alt=""
                    @click="imgDetail($event)"
                    v-else
                />
            </li>
        </ul>
        <div class="c4">
            <p>{{ check && check.checkComment }}</p>
        </div>
        <div class="c5">
            <p>院方/店员确认</p>
        </div>
        <div class="c6">
            <ul>
                <li>
                    <img
                        :src="check && check.orgConfirm"
                        alt=""
                        v-show="check && check.orgConfirm"
                    />
                </li>
            </ul>
        </div>
        <div class="c-i-detail" v-show="showCid">
            <div class="cid-mask" @click="closeCid"></div>
            <img class="cid" src="" alt="" ref="cid" />
            <span @click="closeCid">关闭预览</span>
        </div>

        <div class="vi" v-show="showVideo">
            <div class="vi-mask"></div>
            <div class="vi-content">
                <div class="vic-bg" @click="closeVideoBox"></div>
                <div class="vi-main">
                    <div ref="video"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { initCookie, queryCheckDetailList } from "../../api/check";
// import 'dplayer/dist/DPlayer.min.css';
import DPlayer from "dplayer";
export default {
    data() {
        return {
            checkList: [],
            check: {},
            showCid: false,
            // 显示视频
            showVideo: false,
        };
    },
    created() {
        this.$loading(true, "");
    },
    mounted() {
        this.queryCheckDetailListFn();
    },
    methods: {
        testInit(callback) {
            initCookie({ authCode: "11111111" }).then((result) => {
                console.log("initCookie", result);
                if (result.errorCode === "0") {
                    callback();
                }
            });
        },
        initCookiefn(callback) {
            const _this = this;
            window.yl.call(
                "getAuthcode",
                {},
                {
                    onSuccess: (res) => {
                        console.log("获取getAuthcode:", res);
                        if (res && res.errorCode === "0") {
                            initCookie({ authCode: res.param.authCode }).then(
                                (result) => {
                                    console.log("initCookie", result);
                                    if (result.errorCode === "0") {
                                        callback();
                                    } else if (result.errorCode === "10002") {
                                        window.yl.call(
                                            "requireAuth",
                                            {},
                                            {
                                                onSuccess: function (e) {
                                                    console.log(
                                                        "实名成功-res",
                                                        e
                                                    );
                                                    _this.initCookiefn(
                                                        callback
                                                    );
                                                },
                                                onFail: function (e) {
                                                    window.yl.call(
                                                        "closeWebview"
                                                    );
                                                },
                                            }
                                        );
                                    } else {
                                        _this.$toast.center(result.value);
                                    }
                                }
                            );
                        }
                    },
                    onFail: (res) => {
                        console.log("获取authCode失败");
                    },
                }
            );
        },
        queryCheckDetailListFn() {
            const _this = this;
            const param = {
                checkId: _this.$route.query.checkId,
            };
            console.log("queryCheckDetailList param=", param);

            /* let z = {
                check: {
                    checkId: 2445,
                    supId: "AA45319239AA",
                    orgId: "1003",
                    orgConfirm:
                        "/medical-supervision/file/download/202405/096f32799c0c44cd96d1e4bea0ea56c3.png",
                    checkComment: null,
                    checkTime: "2024-05-09 17:01:02",
                    checkResult: "16项好评；3项非好评；0项备注；2张视图",
                    checkStatus: 2,
                    checkLongitude: null,
                    checkLatitude: null,
                    completeLongitude: null,
                    completeLatitude: null,
                    commentPhotos: null,
                    orgName: null,
                },
                checkDetailList: [
                    {
                        checkId: 2445,
                        listId: 1,
                        checkStatus: "1",
                        checkPhotos:
                            '[{"fileUrl":"http://cdn.hzbanshi.cn/20240509050111393.jpg","fileId":26181},{"imgUrl":"/medical-supervision/file/download/202405/7beecafc6bb34fbd998677f9af5a7972.jpg","imgId":26179,"videoUrl":"/medical-supervision/file/download/202405/8f246122fc954163860202b484846b44.mp4","videoId":26178}]',
                        checkTip: "在营业时间内是否正常营业。",
                        picType: 3,
                        orderNo: 1,
                    },
                    {
                        checkId: 2445,
                        listId: 2,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "是否在显要位置悬挂由甲方制发的协议定点医疗机构或定点零售药店标牌。",
                        picType: 3,
                        orderNo: 2,
                    },
                    {
                        checkId: 2445,
                        listId: 3,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "营业地址，有效期和营业范围等是否与营业执照等证照一致。",
                        picType: 3,
                        orderNo: 3,
                    },
                    {
                        checkId: 2445,
                        listId: 4,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "就医购药流程是否上墙公布",
                        picType: 3,
                        orderNo: 4,
                    },
                    {
                        checkId: 2445,
                        listId: 5,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "是否设立意见箱，投诉举报电话。",
                        picType: 3,
                        orderNo: 5,
                    },
                    {
                        checkId: 2445,
                        listId: 6,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "定点医药机构的收费标准是否公开。",
                        picType: 3,
                        orderNo: 6,
                    },
                    {
                        checkId: 2445,
                        listId: 7,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "定点医药机构是否存在错误宣传医保政策的行为。（如将历年账户支付药品作为“医保药品”宣传，将不符合历年账户支付范围的药品、保健品等标注为“历年账户”宣传）",
                        picType: 3,
                        orderNo: 7,
                    },
                    {
                        checkId: 2445,
                        listId: 8,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "医保管理制度是否落实。",
                        picType: 3,
                        orderNo: 8,
                    },
                    {
                        checkId: 2445,
                        listId: 9,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "定点医药机构是否曲解医保政策，为参保人员提供医疗服务。（如配取驱蚊包、驱蚊香囊等）",
                        picType: 3,
                        orderNo: 9,
                    },
                    {
                        checkId: 2445,
                        listId: 10,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "其他严重违规行为，如遮挡监控、空刷、串换、单方面减免自理、自负部分等。",
                        picType: 3,
                        orderNo: 10,
                    },
                    {
                        checkId: 2445,
                        listId: 11,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "是否张贴打击欺诈骗保等宣传海报",
                        picType: 3,
                        orderNo: 11,
                    },
                    {
                        checkId: 2445,
                        listId: 12,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "信息系统建设是否符合规定。（是否按要求使用人脸识别系统及按规定安装监控等）",
                        picType: 3,
                        orderNo: 12,
                    },
                    {
                        checkId: 2445,
                        listId: 13,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "在岗人员是否有相应的资质。（医师或药师是否与公示的信息一致）",
                        picType: 3,
                        orderNo: 13,
                    },
                    {
                        checkId: 2445,
                        listId: 14,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "是否开展医保内部检查，设置专职科室，配备相应人员。",
                        picType: 3,
                        orderNo: 14,
                    },
                    {
                        checkId: 2445,
                        listId: 15,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip:
                            "是否进行医疗保障基金使用监督管理条例及医保政策的宣传。",
                        picType: 3,
                        orderNo: 15,
                    },
                    {
                        checkId: 2445,
                        listId: 16,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "是否按要求进行人证卡核验。",
                        picType: 3,
                        orderNo: 16,
                    },
                    {
                        checkId: 2445,
                        listId: 17,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "定点零售药店售卖处方药是否按规定售卖处方药",
                        picType: 3,
                        orderNo: 17,
                    },
                    {
                        checkId: 2445,
                        listId: 18,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "是否按要求进行代配药登记。",
                        picType: 3,
                        orderNo: 18,
                    },
                    {
                        checkId: 2445,
                        listId: 19,
                        checkStatus: "1",
                        checkPhotos: "",
                        checkTip: "是否按要求采购使用药品，医疗耗材等。",
                        picType: 3,
                        orderNo: 19,
                    },
                ],
            };
            _this.$loading(false);
            _this.checkList = z.checkDetailList.sort((a, b) => {
                return a.orderNo - b.orderNo;
            });
            return; */
            queryCheckDetailList(param).then((res) => {
                _this.$loading(false);
                console.log("queryCheckDetailList=", res);
                if (res.errorCode === "0") {
                    if (res.data.checkDetailList.length > 0) {
                        _this.checkList = res.data.checkDetailList.sort(
                            (a, b) => {
                                return a.orderNo - b.orderNo;
                            }
                        );
                        // console.log('_this.checkList=', _this.checkList);
                    } else {
                        _this.checkList = [];
                    }
                    // _this.checkList = res.data.checkDetailList
                    _this.check = res.data.check;
                } else if (res.errorCode === "1003") {
                    // _this.testInit(_this.queryCheckDetailListFn);
                    _this.initCookiefn(_this.queryCheckDetailListFn);
                } else {
                    _this.$toast.center(res.value);
                }
            });
        },
        imgDetail(e) {
            this.showCid = true;
            console.log("imgDetail", e.target);
            this.$refs.cid.src = e.target.src;
        },
        closeCid() {
            this.showCid = false;
        },
        scanVideo(v, i) {
            window.location.href = "https://mss.hfi-health.com:9443/" + v;
            return;
            const _this = this;
            _this.showVideo = true;
            _this.dp = new DPlayer({
                container: _this.$refs.video,
                video: {
                    url: v,
                    pic: i,
                    thumbnails: i,
                },
            });
        },
        closeVideoBox() {
            this.showVideo = false;
            this.dp.pause();
        },
    },
    components: {},
    watch: {},
};
</script>
<style lang="less" scoped>
@import "../../assets/css/mixin.less";
.c {
    line-height: 1.5;
    font-weight: 500;
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
    h1.h1-1 {
        position: relative;
        font-size: 32px;
        color: #000;
        padding: 21px 30px 20px 30px;
        &::after {
            .setBottomLine(#ECECEC);
        }
    }
    h1.h1-2 {
        position: relative;
        font-size: 32px;
        color: #000;
        padding: 21px 30px 20px 30px;
    }
    .h1-2v {
        display: flex;
        position: relative;
        padding: 0 30px;
        li {
            position: relative;
            flex: 0 0 218px;
            padding-top: 218px;
            &:nth-child(1) {
                margin-right: 18px;
            }
            &:nth-child(3) {
                margin-left: 18px;
            }
            .i1 {
                position: absolute;
                left: 0;
                top: 0;
                max-height: 100%;
                border-radius: 6px;
                width: 100%;
            }
            .i1-play {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: 20;
                background: url(../../assets/img/<EMAIL>) no-repeat center
                    center;
                background-size: 100px 100px;
            }
            video {
                position: absolute;
                left: 0;
                top: 0;
                width: 208px;
                height: 208px;
            }
        }
    }
    .c1 {
        padding: 34px 30px 0 30px;
        font-size: 32px;
        color: #333;
        position: relative;
        ul {
            display: flex;
            padding: 28px 0 36px 0;
            li {
                flex: 0 0 188px;
                .label {
                    display: inline-block;
                    padding: 14px 54px;
                    border: 1px solid #eee; /*no*/
                    border-radius: 54px;
                    color: #333;
                    font-size: 32px;
                }
                .act {
                    border: 1px solid #317dff; /*no*/
                    background-color: #317dff;
                    color: #fff;
                }
            }
        }
        ul.c2ul {
            position: relative;
            padding: 28px 0 42px 0;
            &::after {
                .setBottomLine(#ECECEC);
            }
        }
    }
    .c1Act {
        &::after {
            .setBottomLine(#ECECEC);
        }
    }
    .c2 {
        ul {
            padding: 0 30px 42px 30px;
            display: flex;
            position: relative;
            li {
                position: relative;
                flex: 0 0 218px;
                padding-top: 218px;
                &:nth-child(1) {
                    margin-right: 18px;
                }
                &:nth-child(3) {
                    margin-left: 18px;
                }
                .i1 {
                    position: absolute;
                    left: 0;
                    top: 0;
                    max-height: 100%;
                    border-radius: 6px;
                    width: 100%;
                }
                .i1-play {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 20;
                    background: url(../../assets/img/<EMAIL>) no-repeat
                        center center;
                    background-size: 100px 100px;
                }
                video {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 208px;
                    height: 208px;
                }
            }
            &::after {
                .setBottomLine(#ECECEC);
            }
        }
    }
    .c4 {
        position: relative;
        padding: 0px 30px 42px 30px;
        &::after {
            .setBottomLine(#ECECEC);
        }
        p {
            font-size: 32px;
            color: #333;
            font-weight: 500;
        }
    }
    .c5 {
        font-size: 32px;
        padding: 21px 30px 20px 30px;
        position: relative;
        display: flex;
        h1 {
            font-size: 32px;
            color: #333;
            font-weight: 500;
        }
    }
    .c6 {
        padding: 8px 30px 68px;
        ul {
            li {
                img {
                    height: 126px;
                    outline: 1px solid #ececec;
                }
            }
        }
    }
    .c-i-detail {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 22;
        .cid-mask {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 1);
        }
        .cid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            max-width: 100%;
            max-height: 100%;
            z-index: 2;
        }
        span {
            display: block;
            position: absolute;
            right: 0;
            top: 0;
            // width: 80px;
            height: 80px;
            line-height: 80px;
            text-align: center;
            background: rgba(0, 0, 0, 0.7);
            z-index: 4;
            font-size: 32px;
            color: white;
            padding: 10px;
        }
    }
    .vi {
        position: fixed;
        bottom: 0;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        .vi-mask {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
        }
        .vi-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            .vi-main {
                padding: 0 30px;
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                width: calc(100% - 60px);
                z-index: 30;
                /deep/ .dplayer {
                    .dplayer-controller {
                        height: 41px; /*no*/
                        padding: 0 20px; /*no*/
                        .dplayer-icons.dplayer-comment-box {
                            height: 38px; /*no*/
                            left: 20px; /*no*/
                            right: 20px; /*no*/
                            .dplayer-icon {
                                padding: 7px; /*no*/
                                width: 40px; /*no*/
                            }
                        }
                        .dplayer-icons.dplayer-icons-left {
                            height: 38px; /*no*/
                            .dplayer-icon.dplayer-play-icon {
                                padding: 7px; /*no*/
                                width: 40px; /*no*/
                                margin: 0 -3px; /*no*/
                            }
                            .dplayer-volume {
                                .dplayer-icon.dplayer-volume-icon {
                                    width: 43px; /*no*/
                                    padding: 7px; /*no*/
                                    margin: 0 -3px; /*no*/
                                }
                                .dplayer-volume-bar-wrap {
                                    margin: 0 10px 0 -5px; /*no*/
                                    .dplayer-volume-bar {
                                        top: 17px; /*no*/
                                        width: 0; /*no*/
                                        height: 3px; /*no*/
                                        .dplayer-volume-bar-inner {
                                            .dplayer-thumb {
                                                right: 5px; /*no*/
                                                margin-top: -4px; /*no*/
                                                margin-right: -10px; /*no*/
                                                height: 11px; /*no*/
                                                width: 11px; /*no*/
                                            }
                                        }
                                    }
                                }
                                [data-balloon-pos="up"]:after {
                                    margin-bottom: 11px; /*no*/
                                }
                                [data-balloon][data-balloon-pos="up"]:after,
                                [data-balloon][data-balloon-pos="up"]:before {
                                    -webkit-transform: translate(
                                        -50%,
                                        10px
                                    ); /*no*/
                                    transform: translate(-50%, 10px); /*no*/
                                }
                            }
                            .dplayer-time {
                                line-height: 38px; /*no*/
                                font-size: 13px; /*no*/
                            }
                        }
                        .dplayer-icons.dplayer-icons-right {
                            right: 20px; /*no*/
                            height: 38px; /*no*/
                            .dplayer-setting {
                                .dplayer-icon.dplayer-setting-icon {
                                    padding: 8px; /*no*/
                                    padding-top: 8.5px; /*no*/
                                    width: 40px; /*no*/
                                }
                                .dplayer-setting-box {
                                    bottom: 50px; /*no*/
                                    width: 150px; /*no*/
                                    border-radius: 2px; /*no*/
                                    padding: 7px 0; /*no*/
                                }
                            }
                            .dplayer-full {
                                .dplayer-icon.dplayer-full-in-icon {
                                    top: -30px; /*no*/
                                    padding: 8px; /*no*/
                                    width: 40px; /*no*/
                                }
                                .dplayer-icon.dplayer-full-icon {
                                    padding: 8px; /*no*/
                                    width: 40px; /*no*/
                                }
                            }
                        }
                        .dplayer-bar-wrap {
                            padding: 5px 0; /*no*/
                            bottom: 33px; /*no*/
                            width: calc(100% - 40px); /*no*/
                            height: 3px; /*no*/
                        }
                    }
                    .dplayer-controller-mask {
                        height: 98px; /*no*/
                    }
                }
            }
            .vic-bg {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 29;
            }
        }
    }
}
</style>
