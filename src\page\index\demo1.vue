<template>
<div>
    <!-- <mt-loadmore :top-method="loadTop"  :bottom-method="loadBottom"  :bottom-all-loaded="allLoaded" ref="loadmore">
        <ul >
            <li v-for="(item,index) in list" :key="index">{{ item }}</li>
        </ul>
    </mt-loadmore> -->
    <div @click="judeAmap" class="div-con">
        高德地图
    </div>
    <div @click="judeBDmap" class="div-con">
        百度地图
    </div>
    <div @click="judeTmap" class="div-con">
        腾讯地图
    </div>

    <a href="androidamap://poi?sourceApplication=dingtalk&keywords=阿里巴巴">打开安卓高德地图</a><br>


<a href="iosamap://poi?sourceApplication=dingtalk&keywords=阿里巴巴">打开iOS高德地图</a><br>

<a href="iosamap://navi?sourceApplication=applicationName&backScheme=applicationScheme&poiname=fangheng&poiid=BGVIS&lat=36.547901&lon=104.258354&dev=1&style=2
">打开iOS高德地图</a><br>




<a href="baidumap://map/place/search?query=阿里巴巴">打开百度地图</a><br>

<a href="baidumap://map/marker?location=40.047669,116.313082&title=我的位置&content=百度奎科大厦&src=ios.baidu.openAPIdemo">地图标点</a><br>


<a href="qqmap://map/search?keyword=阿里巴巴">打开腾讯地图</a><br>


<a href="googlemap://map/search?keyword=阿里巴巴">打开谷歌地图</a><br>


<a href="http://maps.apple.com/?address=${阿里巴巴}">打开谷歌地图</a><br>


<a   href= "http://www.liehuo.net/aaa.asp "   οnclick= "return   getURL(this.href) "> csdn </a><br>

<!-- <a href="http://api.map.baidu.com/marker?location=纬度,经度&title=所在位置名称&content=所在位置的简介（可选）&output=html">百度地图</a> -->
  <a href="http://api.map.baidu.com/direction?origin=22.54605355,114.02597366&destination=22.609562,114.029243&mode=driving&region=深圳&output=html">百度地图</a>

<div>
    <span v-if="isIos == 1">苹果</span>
    <span v-else-if="isIos == 2">安卓</span>
    <div @click="clickmap" style="margin:20px">
        打开地图 
    </div>
</div>

<div>
    <a href="androidamap://navi?sourceApplication=appname&poiname=fangheng&lat=36.547901&lon=104.258354&dev=1&style=2">安卓高德地图</a>
</div>

<a href="bdapp://map/show?center=40.057406655722,116.29644071728
&zoom=11&traffic=on&bounds=37.8608310000,112.5963090000,42.1942670000,118.9491260000&src=andr.baidu.openAPIdemo">展示地图</a>

</div>
    
</template>
<script>
export default {
    data(){
        return {
            list:[],
            allLoaded: false, // 数据是否加载完毕
            isIos:0

        }
    },
    mounted(){
        // this.loadTop()
    },
    methods:{
        clickmap(){
            // Helper.openScheme(
            //   `http://maps.apple.com/?sll=${lat},${lon}&address=${name}`
            // );
            var u = navigator.userAgent;

            var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
            var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
            if(isiOS){
                this.isIos = 1
                
                //苹果自带地图
                window.location.href = `iosamap://poi?sourceApplication=dingtalk&keywords=阿里巴巴`
                
                // window.location.href.openScheme(
                // `http://maps.apple.com/?address='百脑汇'`
                // );
                var timeout = 600;
                let startTime = Date.now();
                let t = setTimeout(function() {  
                    let endTime = Date.now();
                    if (!startTime || endTime - startTime < timeout + 200) {   
                        // alert('请先安装百度地图！');  
                        window.location.href = `baidumap://map/place/search?query=阿里巴巴`


                        // var timeout = 600;
                        let startTime = Date.now();
                        let t = setTimeout(function() {  
                            let endTime = Date.now();
                            if (!startTime || endTime - startTime < 600 + 200) {   
                                // alert('请先安装百度地图！');  
                                window.location.href = `http://maps.apple.com/?address='百脑汇'`

                                let startTime = Date.now();
                                let t = setTimeout(function() {  
                                    let endTime = Date.now();
                                    if (!startTime || endTime - startTime < 600 + 200) {   
                                        alert('请先安装地图！')
                                        // alert('请先安装百度地图！');  
                                        // window.location.href = `http://maps.apple.com/?address='百脑汇'`


                                        
                                    }
                                }, 600);


                                
                            }
                        }, 600);



                    }
                }, timeout);
                window.onblur = function() {  
                    clearTimeout(t);  
                } 
                // setTimeout(function(){
                //     alert('12313')
                //     // window.location.href = `http://maps.apple.com/?address='百脑汇'`
                //    window.location.href =  `baidumap://map/place/search?query=阿里巴巴`
                //     // window.close()
                //     // window.location.href.openScheme(
                //     // `iosamap://viewMap?sourceApplication=applicationName&poiname=${name}&lat=${lat}&lon=${lon}&dev=1`
                //     // `iosamap://poi?sourceApplication=dingtalk&keywords=阿里巴巴`
                //     // );

                //     setTimeout(function(){
                //         window.close()
                //     },1000)


                // },1000)

                
            }else{

                this.isIos = 2
                 //苹果自带地图
                // window.location.href.openScheme(
                // `http://maps.apple.com/?address='百脑汇'`
                // );
                // setTimeout(function(){
                //     window.location.href.openScheme(
                //     // `iosamap://viewMap?sourceApplication=applicationName&poiname=${name}&lat=${lat}&lon=${lon}&dev=1`
                //     `iosamap://viewMap?sourceApplication=applicationName&dev=1`
                //     );


                // },500)

            }
        },
        getURL(url) {
        var xmlhttp = new ActiveXObject( "Microsoft.XMLHTTP");
        xmlhttp.open("GET", url, false);
        xmlhttp.send();
        if(xmlhttp.readyState==4) {
            if(xmlhttp.Status != 200) alert("不存在");
            return xmlhttp.Status==200;
        }
        return false;},
        loadTop(){
            this.getData()
            this.$refs.loadmore.onTopLoaded();
        },
        loadBottom(){
            this.$refs.loadmore.onBottomLoaded();
        },
        getData(){

        
            for (let i = 0; i < 15; i++) {
                this.push(i)
            }


        },
        //百度com.baidu.BaiduMap  高德com.autonavi.minimap 腾讯com.tencent.map
        judeAmap(){
            this.jude('com.autonavi.minimap')
        },
        judeBDmap(){
            this.jude('com.baidu.BaiduMap')
        },
        judeTmap(){
            this.jude('com.tencent.map')
            
        },
        /**
     * 判断客户端是否安装
     */
        jude(packageName) {
//         try {
            var main = plus.android.runtimeMainActivity();
            var packageManager = main.getPackageManager();
            var PackageManager = plus.android.importClass(packageManager);
            var packageInfo = packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            if(packageInfo) {
                //已安装
                alert('已安装')
                return true;
            } else {
                //未安装
                alert('未安装')
                return false;
            }


//         } catch(e) {
//             //未安装
//             return false;
//         }
      }
    }
}
</script>
<style>

.div-con {
    padding: 40px;;
}

</style>