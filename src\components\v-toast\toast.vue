<template>
<div class="toast" v-show="show">
	<div class="mask"></div>
	<div class="content" v-if="isSucess">
		<div class="img1">
			<img src="../../assets/img/<EMAIL>" alt="">
		</div>
		<p>&nbsp;&nbsp;{{tipData}}</p>
	</div>
	<div class="content" v-else>
		<div class="img">
			<img src="../../assets/img/<EMAIL>" alt="">
		</div>
		<p>&nbsp;&nbsp;&nbsp;{{tipData}}</p>
	</div>	
</div>
</template>
<script>
export default {
	data () {
		return {
			isSucess: false,
			show: false,
			tipData: ''
		};
	},
	mounted () {
		// 关闭结果弹窗
		setTimeout(() => {
			this.show = false
		}, 2000);
	},
	methods: {
		setResult (v, tip) {
			this.isSucess = v;
			this.tipData = tip
		},
		open () {
			this.show = true
		},
		close () {
			this.show = false
		}
	},
	components: {},
	watch: {}
};
</script>
<style lang="less" scoped>
.toast{
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 100;
	.mask{
		position: relative;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 101;
	}
	.content{
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translateX(-50%) translateY(-50%);
		width: 624px;
		height: 416px;
		background-color: #fff;
		border-radius: 10px;
		overflow: hidden;
		z-index: 102;
		.img{
			width: 177px;
			margin: 71px auto 0 auto;
			img{
				width: 177px;
			}
		}
		.img1{
			width: 276px;
			margin: 71px auto 0 auto;
			img{
				width: 253px;
			}
		}
		p{
			margin-top: 56px;
			font-size: 38px;
			color: #333;
			font-weight: 800;
			text-align: center;
		}
	}
}
</style>
