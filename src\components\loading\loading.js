import Loading from './loading.vue';

export default {
  install (Vue, options = {}) {
    let loading = null;
    Vue.prototype.$loading = (bool = false, info = '加载中...') => {
			if (!loading) {
				const LoadingObj = Vue.extend(Loading);
				loading = new LoadingObj();
				if (!loading.$el) {
					loading.$mount();
					document.querySelector('body').appendChild(loading.$el);
				}
				loading.setInfo(info);
			}
      if (bool) {
        loading.open();
      } else {
				loading.close();
      }
    };
  }
};
