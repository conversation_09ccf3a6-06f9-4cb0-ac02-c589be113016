<template>
<button class="bsui-btn"
	:class="['bsui-btn_'+ type, 
	'bsui-btn_'+ size, {
		['bsui-btn_'+type+'_disabled']: disabled
	}]"
	:disabled="disabled"
	@click="handleClick"
>
	<slot name="text"></slot>
	<!-- 使用 -->
	<!-- <bsui-button type="default" @click="aa" size="small" disabled>
		<span slot="text">删除订单</span>
	</bsui-button> -->
	<!-- type : default | warn-->
	<!-- size : small | large-->
	<!-- disabled： 不用赋值-->
</button>
</template>
<script>
export default {
	name: 'bsui-button',
	props: {
		type: String,
		size: String,
		// 调用要直接 disabled
		disabled: Boolean
	},
	data () {
		return {};
	},
	mounted () {},
	methods: {
		handleClick (evt) {
			this.$emit('click', evt);
		}
	},
	components: {},
	watch: {}
};
</script>
<style lang="less" scoped>
@import '../../assets/css/mixin.less';
.bsui-btn{
	position: relative;
	display: block;
	margin-left: auto;
	margin-right: auto;
	border: none;
	width: 100%;
	height: 100%;
	padding: 0;
	box-sizing: border-box;
	font-weight: normal;
	font-size: 32px;
	text-align: center;
	text-decoration: none;
	color: #333;
	outline: none;
	border-radius: 2px;/*no*/
	-webkit-tap-highlight-color:rgba(0,0,0,0);
	overflow: hidden;
	white-space: nowrap;
}
.bsui-btn + .bsui-btn{
	margin-left: 30px;
}
.bsui-btn_small{
	display: inline-block;
}
.bsui-btn_large{
	display: block;
}
// 蓝色按钮
.bsui-btn_default {
		background-color: #317DFF;
		border: 1px solid #317DFF; /*no*/
		color: #fff;
    &:not(.bsui-btn_default_disabled):visited {
    }
    &:not(.bsui-btn_default_disabled):active {
				background-color: #6ca2fe;
				border: 1px solid #6ca2fe; /*no*/
    }
}
.bsui-btn_default_disabled {
	background-color: #6ca2fe;
	border: 1px solid #6ca2fe;/*no*/
}
// primary
.bsui-btn_primary {
		background-color: #fff;
		border: 1px solid #ececec; /*no*/
		color: #333;
    &:not(.bsui-btn_primary_disabled):visited {
    }
    &:not(.bsui-btn_primary_disabled):active {
				background-color: rgba(136, 136, 136, 0.6);
				border: 1px solid rgba(136, 136, 136, 0.6); /*no*/
				color: #fff;
    }
}
.bsui-btn_primary_disabled {
	background-color: rgba(136, 136, 136, 0.6);
	border: 1px solid rgba(136, 136, 136, 0.6);/*no*/
	color: #fff;
}
</style>
