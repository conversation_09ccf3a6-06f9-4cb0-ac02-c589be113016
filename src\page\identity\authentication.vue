<template>
    <div class="authentication">
        <div class="middle"><img src="../../assets/img/faceBg.png" alt=""></div>
        <div class="confirm-btn" @touchstart="confirm">开始认证</div>
        <div class="user-info">请确认是<span class="user-name">{{supName}}</span>操作</div>
    </div>
</template>
<script>
import userService from '@/services/userService'
export default {
    data () {
        return {
            supName: '',
            businessNo: ''
        }
    },
    mounted () {
        this.$loading(false, '')
        this.getUserInfo()
    },
    methods: {
       
        async getUserInfo() {
            try {
                // 直接使用首页已初始化并缓存的用户信息
                let info = userService.getUserInfo();
                if (!info || !info.phone) {
                    const initResult = await userService.initUserInfo();
                    if (initResult.success) {
                        info = initResult.userInfo;
                    }
                }
                if (info && (info.userName || info.supName)) {
                    this.supName = info.userName || info.supName || '';
                } else {
                    this.$toast.center('获取用户信息失败');
                }
            } catch (error) {
                console.error('获取用户信息异常:', error);
                this.$toast.center('获取用户信息异常');
            }
        },
       confirm() {
            this.$router.push({
                path: '/sign'
            })
       }
    }
}
</script>
<style lang="less" scoped>
.authentication{
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}
    .middle{
        width:195px;
        height:396px;
        margin:128px auto 81px auto;
        img{
            width:100%;
            height:100%;
        }
    }
    .confirm-btn{
        width:690px;
        height:98px;
        line-height:98px;
        margin:0 auto;
        background:#317dff;
        color:#fff;
        font-size:32px;
        text-align:center;
    }
    .user-info{
        font-size:32px;
        color:#333;
        text-align:center;
        margin-top:50px;
        .user-name{
            color:#317dff;
        }
    }
</style>
