<template>
    <div style="height:100%">
        <!-- <div class="download" style="position: absolute;top:8px;height:25px;text-align:center;color:#FFFFFF;font-size:14px;cursor:pointer;text-shadow: none;" @click="download()">下载</div> -->
        <iframe id="pdf" :src="pdfDoc" width="100%" height="100%"></iframe>
    </div>
</template>
<script>
export default {
    name: 'showPdf',
    data () {
       return {
          pdfUrl: '',
          pdfDoc: '',
          userInfo: {}
       };
    },
    mounted () {
      this.getUrl();
    },
    methods: {
      getUrl () {
        this.pdfUrl = sessionStorage.getItem('pdfUrl')
        this.pdfDoc = './static/pdf/web/viewer.html?file=' + encodeURIComponent(this.pdfUrl);
      },
      // 下载pdf
      confirmDownLoad () {
        var that = this;
        var timestamp = new Date().getTime();
        that.$loading(true, '');
        window.yl.call('downloadfile', { 
          url: that.pdfUrl,
          name: that.$route.query.title
        }, {
          onSuccess: function (a) {
            that.$loading(false, '');
          },
          onFail: function (a) {
            that.$loading(false, '');
            that.$toast.center('下载失败，请稍后再试...');
          }
        });
      },
      download () { 
        var that = this;
        if (window.yl.getSystemInfo().appVersion) {
          this.$confirmBox({
              title: '确认后开始下载', 
              content: '下载完成后请至我的-我的下载中查看',
              okText: '确认',
              cancelCallback: function () { 
              
              },
              okCallback: function () { 
                  that.confirmDownLoad();
              }
          });
        } else {
          this.$confirmBox({
              title: '温馨提示', 
              content: '下载“杭州办事移动”APP，在线打印盖有“红章”的证明',
              cancelText: '',
              okText: '确认',
              cancelCallback: function () { 
              
              },
              okCallback: function () { 
                  
              }
          });
        }
      }
        
  }
};
</script>
<style scoped lang="less">
 .download {
   right: 80px;
 }
</style>
