<template>
<div class="upload" v-if="show">
	<div class="mask" @click="close"></div>
	<div class="content">
		<div class="btn">
			<div class="button" @click="uploadVideo">视频</div>
			<div class="button" @click="uploadImg">图片</div>
		</div>
	</div>
</div>
</template>
<script>
export default {
	data () {
		return {
			show: false
		};
	},
	mounted () {},
	methods: {
		uploadVideo () {
			const _this = this;
			_this.close();
			_this.$loading(true);
			window.yl.call('chooseVideo', {}, {
				onSuccess: function (a) {
					_this.$loading(false);
					console.log('uploadVideo=a', a.param.video);
					_this.$toast.center('上传视频成功');
					_this.$store.commit('setUploadImgs', {
						type: 'video',
						url: a.param.video
					});				
				},
				onFail:function (a) {
					_this.$loading(false);
					console.log('uploadVideo=a', a);
					_this.$toast.center('上传视频失败');
				}
			});
		},
		uploadImg () {
			const _this = this;
			_this.close();
			_this.$loading(true);
			window.yl.call('chooseImage', {}, {
				onSuccess: (res) => {
					_this.$loading(false);
					_this.$toast.center('上传图片成功');
					_this.$store.commit('setUploadImgs', {
						type: 'img',
						url: res.param.imgurl
					});
					// this.$store.commit('setUploadImgs', res.param.imgurl);
				},
				onFail: (res) => {
					_this.$loading(false);
					// console.log('上传图片失败==', res)
					_this.$toast.center('上传图片失败');
				}
			})
		},
		open () {
			this.show = true;
		},
		close () {
			this.show = false;
		}
	},
	components: {},
	watch: {
		show (nv, ov) {
			if (nv) {
				// console.log('nv', nv);
				this.$store.commit('setHide', true)
			} else {
				this.$store.commit('setHide', false)
			}
		}
	}
};
</script>
<style lang="less" scoped>
.upload{
	position: fixed;
	top: 0;
	bottom: 1px;
	left: 0;
	right: 0;
	z-index: 100;
	.mask{
		position: relative;
		width: 100%;
		height: 100%;
		z-index: 101;
		background: rgba(0, 0, 0, 0.5)
	}
	.content{
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 500px;
		background: #fff;
		z-index: 102;
		.btn{
			display: flex;
			height: 100px;
			line-height: 100px;
			.button{
				flex: 1;
				text-align: center;
				height: 100%;
				background: #f7f7f7;
				&:nth-child(1){
					border-right: 1px solid #fff;
				}
			}
		}
	}
}
</style>
