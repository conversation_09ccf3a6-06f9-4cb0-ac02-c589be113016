/**
 * Toast 样式
 */
.lx-toast {
  position: fixed;
  bottom: 1.44rem;
  left: 50%;
  box-sizing: border-box;
  max-width: 80%;
  padding: 0.144rem 0.288rem;/*no*/
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  text-align: center;
  z-index: 9999;
  font-size: 0.3rem;/*no*/
  color: #fff;
  border-radius: 0.07rem;/*no*/
  background: rgba(0, 0, 0, 0.7);
  animation: show-toast .5s;
  -webkit-animation: show-toast .5s;
  overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;
}

.lx-toast.lx-word-wrap {
  width: 80%;
  white-space: inherit;
  height: auto;
}

.lx-toast.lx-toast-top {
  top: 0.7rem;/*no*/
  bottom: inherit;
}

.lx-toast.lx-toast-center {
  top: 50%;
  margin-top: -0.288rem;/*no*/
  bottom: inherit;
}

@keyframes show-toast {
  from {
    opacity: 0;
    transform: translate(-50%, -0.144rem);/*no*/
    -webkit-transform: translate(-50%, -0.144rem);/*no*/
  }

  to {
    opacity: 1;
    transform: translate(-50%, 0);
    -webkit-transform: translate(-50%, 0);
  }
}