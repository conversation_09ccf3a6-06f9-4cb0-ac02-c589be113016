<template>
  <div id="app">
    <router-view/>
	<div class="btn-ceshi" @click="ceshi"></div>
	<div class="password" v-show="inputShow"><input type="text" v-model="val"> <span @click="btn" class="confirm-ceshi">确定</span></div>
  </div>
</template>

<script>
import vConsole from 'vconsole'
export default {
	name: 'App',
	data () {
		return {
			n: 0,
			val: '',
			inputShow: false
		}
	},
	mounted() {
		this.cancel()
	},
	methods: {
		ceshi () {
			this.n++
			if(this.n >= 8) {
				this.inputShow = true
			}
		},
		btn () {
			if(this.n >= 8) {
				if(this.val === '12345678') {
					new vConsole()
				} else {
					this.inputShow = false
				}
			}
		},
		cancel () {
			window.yl.call("setTitleBarLeftButton", {
				leftButtonShow : true ,
				leftButtonText : "" ,
				leftButtonTextColor : "FF00FF00" ,
				leftButtonIcon : "icon-user" ,
				showCloseButton : false,

			},{
				onSuccess:function (a) {
					console.log('success')
				},
				onFail:function (a) {
					console.log('fail')
				}
			})

		}
	}
}
</script>

<style>
#app{
	height: 100%;
	overflow: visible;
}
.btn-ceshi{
	position:fixed;
	/* border:1px solid #ddd; */
	left:0;
	bottom:0;
	width:50px;
	height:50px;
	z-index:99999;
}
.password{
	position:fixed;
	right:0;
	bottom:0;
	z-index:99999;
}
.confirm-ceshi{
	width:100px;
	height:100px;
	display:inline-block;
}
</style>
