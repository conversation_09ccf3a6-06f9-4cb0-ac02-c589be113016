import { BASE_URL_MEDICAL } from './config';
import axios from 'axios';
//获取检查历史记录草稿箱列表
export function queryHistoryChecks (obj) {
    const url = BASE_URL_MEDICAL + 'check/queryHistoryChecksByDate';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}
//获取检查历史详情页
export function queryCheckDetailList (obj) {
    const url = BASE_URL_MEDICAL + 'check/queryCheckDetailList';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}

//获取消息通知
export function queryMessagePush (obj) {
    const url = BASE_URL_MEDICAL + 'supervisor/queryMessagePush';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}

//查询历史检查列表（按日期分组）
export function queryHistoryChecksByDate (obj) {
    const url = BASE_URL_MEDICAL + 'check/queryHistoryChecksByDate';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}