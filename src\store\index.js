import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
// 简单模式
export default new Vuex.Store({
	state: {
		sign: null,
		uploadImgs: [],
		isHide: false,
		code: '',
		locationItem: {},
		isShowCorrectTips: false
	},
	// this.$store.getters.isHide
	getters: {
		getSign(state) {
			return state.sign
		},
		getUploadImgs(state) {
			return state.uploadImgs
		},
		getHide(state) {
			return state.isHide
		},
		code(state) {
			return state.code
		},
		locationItem(state) {
			return state.locationItem
		},
		isShowCorrectTips(state) {
			return state.isShowCorrectTips
		}
	},
	// this.$store.commit('setHide', {})
	mutations: {
		setSign(state, v) {
			state.sign = v
		},
		// t: checkPhotos|checkStatus;
		// hasVal: 1有值；2无值；0是初始无值null
		setUploadImgs(state, v) {
			// 是否存在listId ，listId存在即存在
			let flag = state.uploadImgs.some(function (item) {
				// return item.checkId === v.checkId && item.listId === v.listId && (item.checkStatus === null || item.checkStatus === v.checkStatus);
				return item.checkId === v.checkId && item.listId === v.listId;
			});
			if (flag) {
				state.uploadImgs = state.uploadImgs.map(item => {
					if (item.checkId === v.checkId && item.listId === v.listId) {
						return {
							'checkId': v.checkId,
							'listId': v.listId,
							'checkPhotos': v.checkPhotos.slice(),
							'checkStatus': v.checkStatus,
							'hasVal': v.hasVal
						}
					} else {
						return item;
					}
				})
			} else {
				state.uploadImgs.push({
					'checkId': v.checkId,
					'listId': v.listId,
					'checkPhotos': v.checkPhotos.slice(),
					'checkStatus': v.checkStatus,
					'hasVal': v.hasVal
				});
			}
		},
		deleteUploadImgs(state, v) {
			if (v) {
				state.uploadImgs = [];
			}
		},
		setHide(state, v) {
			state.isHide = v;
		},
		setCode(state, v) {
			state.code = v;
		},
		updateLocationItem(state, v) {
			state.locationItem = v
		},
		updateShowTipStatus(state, v) {
			state.isShowCorrectTips = v
		}
	},
})