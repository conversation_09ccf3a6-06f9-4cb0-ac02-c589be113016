<template>
<div class="sign" v-show="show">
	<div class="content">
		<h2>
			<div class="cslt" @click="close"></div>
			<div class="csct">签字</div>
			<div class="csrt"></div>
		</h2>
		<div class="canvas">
			<canvas ref="canvas" @touchstart="touchstartFn" @touchmove="touchmoveFn" @touchend="touchendFn"></canvas>
		</div>
		<div class="btn">
			<div class="btn-lt"  @click="clearCanvas">清除</div>
			<div class="btn-rt" @click="saveCanvas">保存</div>
		</div>
	</div>
</div>
</template>
<script>
import vButton from '../../components/v-button/button'
import {initCookie, upload, getFileToken} from '../../api/check'
export default {
	data () {
		return {
			canvas: null,
			cxt: null,
			show: false,
			url:'',
			canvasOffset: ''
		};
	},
	mounted () {
		this.lineCanvas()
	},
	methods: {
		initCookiefn (callback) {
			const _this = this;
			window.yl.call('getAuthcode', {}, {
				onSuccess: (res) => {
					console.log('获取getAuthcode:', res);
					if (res && res.errorCode === '0') {
						initCookie({authCode: res.param.authCode}).then((result) => {
							console.log('initCookie', result);
							if (result.errorCode === '0') {
								callback();
							} else if (result.errorCode === '10002') {
								window.yl.call('requireAuth', {}, {
									onSuccess: function (e) {
										console.log('实名成功-res', e);
										_this.initCookiefn(callback);
									},
									onFail: function (e) {
										window.yl.call('closeWebview');
									}
								});
							} else {
								_this.$toast.center(result.value)
							}
						});
					}
				},
				onFail: (res) => {
					console.log('获取authCode失败');
				}
			});
		},
		open () {
			this.show = true
		},
		close () {
			// console.log('关闭签名');
			this.$store.commit('setHide', false)
			this.show = false
			this.clearCanvas();
		},
		showImg (url) {
			this.url = url;
		},
		clearCanvas () {
			// 624,540
			this.cxt.clearRect(0, 0, 312, 270);
			this.cxt.fillStyle = '#ffffff';
			this.cxt.fillRect(0, 0, 312, 270);
		},
		saveCanvas () {
			const _this =this;
			_this.$loading(true, '');
			var imgBase64 = _this.canvas.toDataURL('image/png');
			_this.uploadFn(_this.convertBase64UrlToBlob(imgBase64, 'sign'));
		},
		// 上传图片
		uploadFn (file) {
			const _this = this;
			getFileToken().then(token => {
				console.log('getFileToken=', token);
				if (token.errorCode.toString() === '0') {
					const fd = new FormData();
					fd.append('file', file);
					fd.append('fileToken', token.data);
					upload(fd).then(res => {
						_this.$loading(false, '');
						console.log('upload res=', res);
						if (res.errorCode.toString() === '0') {
							_this.$store.commit('setSign', res.data.fileUrl);
							_this.url = res.data.fileUrl;
							_this.$toast.center('保存成功');
							setTimeout(() => {
								_this.close()
							}, 500);
						} else {
							_this.$toast.center(res.value);
						}
					}).catch(error => {
						_this.$toast.center('接口异常')
					})
				} else if (token.errorCode.toString() === '1003'){
					_this.initCookiefn(_this.saveCanvas)
				} else {
					_this.$toast.center(token.value);
				}
			}).catch(error => {
				_this.$toast.center('接口异常')
			})
		},
		convertBase64UrlToBlob(dataurl, filename) {
			var arr = dataurl.split(','),
			mime = arr[0].match(/:(.*?);/)[1],
			bstr = atob(arr[1]),
			n = bstr.length,
			u8arr = new Uint8Array(n);
			while(n--){
				u8arr[n] = bstr.charCodeAt(n);
			}
			// console.log('image type=', mime);
			return new File([u8arr], filename, {type:mime});
		},
		lineCanvas () {
			this.canvas = this.$refs.canvas;
			this.cxt = this.canvas.getContext("2d");
			this.canvas.width = 312;
			this.canvas.height = 270;
			this.cxt.fillStyle = '#ffffff';
			this.cxt.fillRect(0, 0, 312, 270);
			this.cxt.strokeStyle = '#000000';
			this.cxt.lineWidth = 2;
			this.cxt.lineCap = "round";
		},
		touchstartFn (e) {
			const _this = this;
			_this.canvasOffset = _this.$refs.canvas.getBoundingClientRect();
			// console.log('touchstartFn e', e.targetTouches[0].pageX , e.targetTouches[0].pageY);
			_this.cxt.beginPath();
			_this.cxt.moveTo(e.changedTouches[0].pageX - 15 - _this.canvasOffset.left, e.changedTouches[0].pageY - 15 - _this.canvasOffset.top);
		},
		touchmoveFn (e) {
			const _this = this;
			_this.cxt.lineTo(e.changedTouches[0].pageX - 15 - _this.canvasOffset.left, e.changedTouches[0].pageY - 15 - _this.canvasOffset.top);
			_this.cxt.stroke();
		},
		touchendFn (e) {
			this.cxt.closePath();
		}
	},
	components: {
		vButton
	},
	watch: {
	}
};
</script>
<style lang="less" scoped>
@import '../../assets/css/mixin.less';
.sign{
	position: fixed;
	bottom: 0;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
	background-color: rgba(0, 0, 0, 0.3);
	.content{
		position: absolute;
		left: 50%;
		top: 50%;
		margin-left: -312px;
		margin-top: -375px;
		width: 624px;
		height: 740px;
		background: #fff;
		border-radius: 10px;
		overflow: hidden;
		h2{
			position: relative;
			height: 110px;
			line-height: 110px;
			display: flex;
			.cslt{
				flex: 0 0 110px;
				height: 100%;
				background: url(../../assets/img/<EMAIL>) no-repeat 28px center;
				background-size: 17px 34px;
			}
			.csct{
				flex: auto;
				text-align: center;
				font-size: 38px;
				color: #333;
				font-weight: bold;
			}
			.csrt{
				flex: 0 0 110px;
			}
			&::after{
				.setBottomLine(#ECECEC);
			}
		}
		.canvas{
			position: relative;
			canvas{
				width: 624px;
				height: 540px;
			}
			&::after{
				.setBottomLine(#ECECEC);
			}
		}
		.btn{
			display: flex;
			font-size: 32px;
			height: 87px;
			line-height: 87px;
			font-weight: 500;
			text-align: center;
			.btn-lt{
				flex: 1;
				color: #333;
			}
			.btn-rt{
				flex: 1;
				color: #317DFF;
				position: relative;
				&::after{
					.setLeftLine(#ECECEC);
				}
			}
		}
	}
}
</style>
