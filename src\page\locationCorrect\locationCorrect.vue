<template>
    <div class="correct-content">
        <div class="correct-content-row">
            <div class="con-title">
                {{typeInfo.typeTitle}}
            </div>
            <div class="con-subtitle">
                {{typeInfo.typeSubTitle}}
            </div>
        </div>
        <div class="main-con">
            <div>
                <div class="opre-title">
                    {{typeInfo.opreTitle}}
                </div>
                <div>
                    <div class="orga-con">
                        <!-- <span class="orga-con-left">机构名称</span>
                        <span class="orga-con-right">{{item.orgName}}</span> -->
                        <span class="orga-con-left">机构名称：</span>
                        <span class="orga-con-right">{{item.orgName}}</span>
                    </div>
                
                    <div class="orga-con">
                        <span class="orga-con-left">机构类型：</span>
                        <span class="orga-con-right">{{item.orgType ==  1 ? '药店':'医院'}}</span>
                    </div>
                    
                </div>
            </div>
            <div class="location" @touchstart="updateLocation">
                <div class="bgImg">
                   <img :src="item.bgImg" v-if="item.bgImg" />
                   <img src="@/assets/img/amap.png" v-else>
                </div>
                <div class="bgCon">
                </div>
                <div class="locationCon">
                    <!-- <div> -->
                        <img src="@/assets/img/update.png" class="location-img"/>
                    <!-- </div> -->
                    <span>点击更新地图上的位置</span>
                </div>
            </div>
            <div class="pb25">
                <div ref="addressCon" class="address-con">
                    <span>
                        地 址：
                    </span>
                    <div ref="inputDiv" contenteditable class="address-con-input"  @focus="focusInput" @blur="blurInput" @input="inputValue">
                    </div>
                    <div class="clear-icon" @touchstart="clearInput" v-if="isShowClear">
                    </div>

                </div>
            <div :class="isFocus ? 'b-line active-line':'b-line'">
            </div>
            </div>
           
        </div>
         <div class="tips"  v-if="isShowToolTip">
                <div class="tooltip">
                  记得完善门牌号哦
                </div>
            </div>
        <div>
            <div class="submit-btn" :class="btnDisable ? '' : 'btnDisable'" @touchstart="submit">
                确认提交
            </div>
        </div>
        <modal :show="modalShow" :showClose="showClose" @close="closeModal">
            <div>
                <div class="success-icon">
                </div>
                <div class="custom-modal-content-title">
                    <div v-if="type == 1">
                        <p>感谢您提供修改意见，</p>
                        <p>我们会尽快核准并更正。</p>   
                    </div>
                    <div v-else>
                        感谢您的补充！
                    </div>
                </div>
                <div class="btn-search" v-if="type == 2" @click.stop="sign">
                    前往签到
                </div>
            </div>
        </modal>
    </div>
</template>
<script>
import modal from '@/components/customModal/modal.vue'
import defaultAmap from '@/assets/img/amap.png'
import {addRecoveryOrgan} from '@/api/sign'
export default {
    name:'locationCorrect',
    components:{
        modal
    },
    props:{
        // item:{
        //     type:Object,
        //     default:function(){
        //         return {}
        //     }
        // }
    },
    data(){
        return {
            modalShow:false,
            showClose:false,
            item:{},
            typeInfo:{},
            type: 1,
            isFocus:false,
            isShowClear: false,
            isShowToolTip:false,
            timer:null,
            btnDisable:false
        }
    },
    mounted(){
        this.type = this.$route.params.id
        this.isShowToolTip = this.$store.getters.isShowCorrectTips
        if(this.type == 1){
            this.typeInfo.typeTitle = '修改信息'
            this.typeInfo.typeSubTitle = '修改机构名称/类型/实际所在地等信息'
            this.typeInfo.opreTitle = '你要报错的地点是：'
        }else{
            this.typeInfo.typeTitle = '补充机构经纬度'
            this.typeInfo.typeSubTitle = '我已找到该机构的实际坐标位置'
            this.typeInfo.opreTitle = '您要补充坐标的地点是：'
        }
        this.item = Object.assign({},this.$store.getters.locationItem)
        this.$refs.inputDiv.innerText = this.item.orgAddress
        this.judeBtnStatus()
    },
    methods:{
        judeBtnStatus(){
            if(this.$refs.inputDiv.innerText === '' || this.$refs.inputDiv.innerText === null || this.$refs.inputDiv.innerText === undefined)
            {
                this.btnDisable = false
            }else{
                this.btnDisable = true
            }
        },
        closeModal(){
            this.modalShow = false
        },
        inputValue(e){
          if(this.$refs.inputDiv.innerText === '' || this.$refs.inputDiv.innerText === null || this.$refs.inputDiv.innerText === undefined){
             this.isShowClear = false
          }else{
              this.isShowClear = true
          }
          this.judeBtnStatus()
        },
        clearInput(){
            this.$refs.inputDiv.innerText = ''
            this.isShowClear = false
        },
        focusInput(){
            this.isFocus = true
            this.isShowClear = true
            clearTimeout(this.timer)
        },
        blurInput(){
            this.isFocus = false
            this.isShowClear = false
            this.timer = setTimeout(() => {
                window.scrollTo(0, 0);
            }, 100)
        },
        updateLocation(){
            this.$router.push({
                path:'/updatePosition/' + this.type
            })
        },
        sign(){
            this.$router.replace({
                path:'/sign'
            })
        },
        submit(){
            if(this.$refs.inputDiv.innerText === '' || this.$refs.inputDiv.innerText === null || this.$refs.inputDiv.innerText === undefined){
                this.$toast.center('请填写完整的机构信息')
                return
            }
            this.$loading(true, '');
            var that = this
            var subItem = Object.assign({},that.item)
            subItem.submitType = this.type == 1 ? 1 : 0
            subItem.orgAddress = this.$refs.inputDiv.innerText
            delete subItem.bgImg
            delete subItem.isUpdate
            delete subItem.orgAreasBelongsStr
            delete subItem.orgBelongsStreetStr
            that.getRegeCode(subItem.orgLongitude,subItem.orgLatitude,subItem) 
        },
        
        addRecoveryOrgan(item){
            var that = this
            addRecoveryOrgan(item).then(res=>{
                that.$loading(false, '');
                if(res.errorCode == 0){
                    that.modalShow = true
                    if(that.type == 1){
                        setTimeout(() => {
                            that.sign()
                        }, 2000);
                    }
                }else{
                   that.$toast.center(res.value)
                }
            }).catch(error=>{
                that.$loading(false, '');
            })
        },

        getRegeCode(lnt,lat,item){
			var that = this
			AMap.plugin('AMap.Geocoder',function(){//异步加载插件
				var geocoder = new AMap.Geocoder({
				city: "全国",
				radius: 1000 
			});
			var lnglat = [lnt,lat]
			geocoder.getAddress(lnglat, function(status, result) {
				if (status === 'complete'&&result.regeocode) {
					that.$set(item,'orgAreasBelongs',result.regeocode.addressComponent.district)
					that.$set(item,'orgBelongsStreet',result.regeocode.addressComponent.township)	
                }
                that.addRecoveryOrgan(item)
			});
			});
		},
    }
}
</script>
<style scoped lang="less">
.pb25 {
    padding-bottom: 50px;
}
.tooltip {
    color: white;
    font-size: 32px;
    background-image: url(../../assets/img/toolTip.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 20px 10px;
    text-align: center;
    margin-top: -70px;
}
.tips {
    width: 292px;
    height: 81px;
    margin-left: auto;
    margin-right: 60px;
}
.tips-arrow {
    position: absolute;
    /* top: -0.08rem; */
    border-top-width: 0;
    /* border-bottom-color: #303133; */
    top:-24px;
    border-width: 12px;
    border-style: solid;
    width: 0;
    border-color: transparent;
    border-bottom-color: #7b6e6e;
    width: 0px;
    height: 0px;
 &::after{
    top: 1px;
    margin-left: -15px;
    border-top-width: 0;
    border-bottom-color: #7b6e6e;
    content: " ";
    border-width: 12px;


 }

}
.letterspace {
   letter-spacing:0.3rem
}

.con-title {
   color: #000000;
   font-size: 32px;
   margin-bottom: 23px;
}

.con-subtitle {
   color: #777777;
   font-size: 30px;
}
.mb20 {
    margin-bottom: 20px;
}
.main-con {
    background:#F8F8F8;
    margin: 30px;
    padding: 0px 30px;
    border-radius:10px;
}
.opre-title {
   color: #333333;
   font-size: 32px;
   margin-bottom: 50px;
   padding-top: 50px;
}
.orga-con{
    font-size: 32px;
    color: #5D5D5D;
    line-height: 50px;
    &:first-child {
        padding-top: 0px;
        margin-bottom: 15px;
    }
}

.address-con {
    display: flex;
    align-items: center;
    font-size: 32px;
    line-height: 50px;
    &-input {
        width: 100%;
        box-sizing: border-box;
        flex: 1;
        border: none;
        padding: 20px;
        font-size: 32px;
        color: #333333;
        background-color: #F8F8F8;
        &:focus {
            outline: none;
            border:none
        }
    }
}
.clear-icon {
    width: 32px;
    height: 32px;
    background: url(../../assets/img/<EMAIL>) no-repeat center center;
    background-size: 32px 32px;
    
}

.b-line {
    background: #e8e3e3;
    height: 2px;
    width: 100%;
}
.active-line {
    background: #317DFF;
}

.submit-btn {
    width:690px;
    height:98px;
    line-height: 98px;
    color:#fff;
    font-size:32px;
    text-align:center;
    background:#317dff;
    margin:45px auto 0 auto;
    border-radius:10px;
    &.btnDisable{
		background:rgba(49,125,255,0.4);
		color:#FFFFFF;
	}
}
.location {
    text-align: center;
    // padding: 56px 0px;
    margin: 50px 0px;
    background: rgba(0,0,0,0.3);
    border-radius: 8px;
    position: relative;
    height: 160px;
    .bgImg {
        width: 100%;
        height: 100%;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    .bgCon {
        height: 100%;
        width: 100%;
        position: absolute;
        left: 0;
        width: 100%;
        top: 0;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 8px;
    }
    .locationCon {
        width: 100%;
        position: absolute;
        left: 0;
        top: 50%;
        height: 50px;
        margin-top: -30px;
        // background: rgba(0,0,0,0.3);
    }
    span {
        font-size: 32px;
        font-weight: bold;
        color: white;
        vertical-align: middle;
        text-shadow:0px 4px 12px rgba(61, 61, 61, 0.35);
    }
    .location-img {
        // width: 49px;
        height: 100%;
        vertical-align: middle;
        margin-right: 10px;
    }
}
.correct-content {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    &-row{
        padding:20px;
        padding-top: 40px;
    }
}

.position-input {
    border: none;
    padding: 0.2rem;
    font-size: 30px;
    &:focus {
        outline: none;
    }
}
.spline {
    width: 100%;
    height: 1px;
    background-color: #f0e7e7;
    &-top {
        margin-top:20px;
    }
}

</style>