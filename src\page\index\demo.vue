<template>
    <div>
        <ul class="mui-table-view" v-infinite-scroll="loadMore" infinite-scroll-disabled="moreLoading" infinite-scroll-distance="0" infinite-scroll-immediate-check="false">
            <li class="mui-table-view-cell ceshi" v-for="(item,index) in list" :key="index">
                <a class="mui-navigate-right">
                    <span class="mui-pull-left">{{item.date}}</span>
                </a>
            </li>
            <!--底部判断是加载图标还是提示“全部加载”-->
            <li class="more_loading" v-show="!queryLoading">
                <mt-spinner type="snake" color="#00ccff" :size="20" v-show="moreLoading&&!allLoaded"></mt-spinner>
                <span v-show="allLoaded">已全部加载</span>
            </li>
        </ul>
    </div>
</template>
<script>
import { queryHistoryChecksByDate } from '@/api/history'
import { initCookie } from '@/api/check'
export default {
    data () {
        return {
            list: [],
            queryLoading: false,
            moreLoading: false,
            allLoaded: false,
            totalNum: 0,
            pageSize: 20,
            pageNum: 1
        }
    },
    mounted() {
        this.loadMore()
    },
    methods: {
        //无限加载函数
        loadMore() {
            if(this.allLoaded){
                this.moreLoading = true;
                return;
            }
            if(this.queryLoading){
                return;
            }
            this.moreLoading = !this.queryLoading;
            this.pageNum++;
            queryHistoryChecksByDate({
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                checkStatus: 1
            }).then((res)=>{
                if(res.data) {
                    res.data = [
                        {
                            date:'2019-09-09'
                        },
                        {
                            date:'2019-09-09'
                        },
                        {
                            date:'2019-09-09'
                        },
                        {
                            date:'2019-09-09'
                        },
                        {
                            date:'2019-09-09'
                        },
                    ]
                    this.list = this.list.concat(res.data);
                    this.allLoaded = res.data.length == 0;
                }
                this.moreLoading = this.allLoaded;
            })
        },
    }
}
</script>
<style>
.ceshi{
    height:200px;
    border:1px solid #ddd;
}
</style>
