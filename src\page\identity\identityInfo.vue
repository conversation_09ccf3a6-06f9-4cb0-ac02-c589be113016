<template>
    <div class="identityInfo">
        <div class="user-con">
            <div class="user-img">
                <img :src="supPhoto" alt="">
            </div>
        </div>
        <ul class="user-info">
            <li class="user-list">
                <div class="info-title">杭州市医疗保障监督员</div>
                <div class="user-name">{{supName}}</div>
            </li>
             <li class="user-list">
                <div class="info-title">有效期</div>
                <div class="user-name">{{supJoinedDate}} ~ {{supInvalidDate}}</div>
            </li>
        </ul>
    </div>
</template>
<script>
import  { mapState } from 'vuex'
import { getSupervisorByCode } from '@/api/identity'
export default {
    data () {
        return{
            code: '',
            supName: '',
            supPhoto: '',
            supJoinedDate: '',
            supInvalidDate: ''
        }
    },
    mounted() {
        console.log(this.$route.query.code)
        this.$loading(true, '')
        this.code = this.getUrlKey('code')
        this.getSupervisorByCode(this.code)
    },
    methods: {
        getSupervisorByCode (code) {
            console.log(code)
            getSupervisorByCode({
                'code': code
            }).then((res)=>{
                console.log(33, res)
                if(res.errorCode === '0') {
                    this.supName = res.data.supName
                    this.supPhoto = res.data.supPhoto
                    this.supJoinedDate = res.data.supJoinedDate
                    this.supInvalidDate = res.data.supInvalidDate
                    if(res.data.supStatus !== 1) { // 不在职
                        this.$toast.center('监督员身份已失效')
                    }
                } else {
                    this.$toast.center(res.value)
                }
                this.$loading(false, '')
            }).catch((err)=>{
                console.log(err)
            })
        },
        getUrlKey (name) {
			return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [0, ''])[1].replace(/\+/g, '%2B')) || null
		},
    }
}
</script>
<style lang="less" scoped>
    .identityInfo{
        overflow: auto;
        height: 100%;
        -webkit-overflow-scrolling: touch;
    }
.user-con{
    border-bottom:1px solid #ECECEC;
    .user-img{
        width:321px;
        height:423px;
        margin:63px auto 86px auto;
        img{
            width:100%;
            height:100%;
        }
    }
}
.user-info{
    .user-list{
        display:flex;
        justify-content: space-between;
        padding:0 30px;
        border-bottom:1px solid #ECECEC;
        height:88px;
        line-height:88px;
        .info-title{
            font-size:32px;
            color:#333;
        }
        .user-name{
            font-size:32px;
            color:#777;
        }
    }
}
</style>
