//配置项
const BASE_URL = process.env.NODE_ENV === 'development' ? '/api1' : '/';
const BASE_URL_MEDICAL = process.env.NODE_ENV === 'development' ? '/medical-supervision/' : '/medical-supervision/';
let AUTH_URL_MEDICAL;
if (process.env.NODE_ENV === 'development') {
    // AUTH_URL_MEDICAL = '/api2/hzeg-app-service/';
    AUTH_URL_MEDICAL = 'https://service.citybrain.hangzhou.gov.cn/items-service/';
} else if (process.env.NODE_ENV === 'test') {
    AUTH_URL_MEDICAL = 'https://app.xjbsefjz.fun:11180/items-service/';
} else {
    AUTH_URL_MEDICAL = 'https://service.citybrain.hangzhou.gov.cn/items-service/';
}
export {
    BASE_URL,
    AUTH_URL_MEDICAL,
    BASE_URL_MEDICAL
};
