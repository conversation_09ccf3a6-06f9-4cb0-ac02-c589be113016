<template>
     <div class="custom-modal" v-show="show">
        <div class="custom-modal-body">
            <div class="custom-modal-content">
                <slot>
                </slot>
            </div>
        </div>
        <div class="close-icon" @touchstart="close" v-show="showClose">
        </div>
　　</div>
</template>
<script>
export default {
    props:{
        show:{
            type:Boolean,
            default:false
        },
        showClose:{
            type:Boolean,
            default:true
        },
    },
    data(){
      return {

      }
    },
    methods:{
        close(){
            this.$emit('close')
        }
    }
}
</script>
<style lang="less" scoped>
.custom-modal{
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.3);
	&-spLine {
		width: 100%;
		height: 1px;
		background-color: #f0e7e7;
	}
	&-body {
		position: absolute;
		top: 25%;
		left: 50px;
		right: 50px;
        border-radius: 8px;
        background: white;
		background-repeat: no-repeat;
		background-size: 100%;
	}
	&-content {
        margin: 80px 0px 40px;
		&-title {
		    color: #333333;
            font-size: 36px;
            line-height: 52px;
            text-align: center;
            margin: 50px 40px;
		}
    }
    .error-icon {
        background-image: url(../../assets/img/<EMAIL>);
		background-repeat: no-repeat;
		background-size: 100%;
		height: 177px;
		width: 177px;
        margin: auto;
    }
    .success-icon {
        background-image: url(../../assets/img/<EMAIL>);
		background-repeat: no-repeat;
		background-size: 100%;
		height: 177px;
		width: 235px;
        margin: auto;
    }
    .btn-search {
        background: #317DFF;
        padding: 30px 60px;
        font-size: 32px;
        color: white;
        text-align: center;
        width: 335px;
        margin: auto;
        border-radius: 8px;
    }
	.close-icon {
		background-image: url(../../assets/img/close.png);
		background-repeat: no-repeat;
		background-size: 72px 72px;
		height: 72px;
		width: 72px;
		position: absolute;
		bottom: 60px;
		left: 50%;
		margin-left: -36px;
	}
}
</style>

