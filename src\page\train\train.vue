<template>
    <div class="train-con">
        <div class="train">
            <h2 class="train-title">{{infoTitle}}</h2>
            <div class="train-content" v-html="infoContent" @click="clickPDF"></div>
         </div>
    </div>
</template>
<script>
import { selectTrainingInfo } from '@/api/train'
export default {
    data () {
        return{
            infoContent: '',
            infoTitle: ''
        }
    },
    mounted () {
        console.log(this.$route.query.id)
        this.$loading(true, '');
        this.selectTrainingInfo()
    },
    methods: {
        clickPDF(e){
            console.log(e)
            console.log(e.target)
            console.log(e.target.href)
            console.log('1212')
            if(e.target.href){
                if(e.target.href.indexOf('.pdf') > -1 || e.target.href.indexOf('.PDF') > -1){
                    event.preventDefault();
                    sessionStorage.setItem('pdfUrl',e.target.href)
                    this.$router.push({
                        path:'showPdf',
                        // query: {
                        //     title: e.target.title
                        // }
                    })
                }
            }
        },
        selectTrainingInfo () {
            selectTrainingInfo({
                infoId: this.$route.query.id
            }).then((res)=>{
               console.log(res)
               if(res.errorCode ==='0'){
                   this.infoContent = res.data.infoContent
                   this.infoTitle = res.data.infoTitle
                   this.$loading(false, '');
               } else if(res.errorCode ==='1003') {
                   this.$toast.center(res.value)
               }
            }).catch((err)=>{
                console.log(err)
            })
        }
    }
}
</script>
<style lang="less" scoped>
.train-con{
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}
.train{
    padding:30px;
    .train-title{
        font-size:32px;
        color:#000;
        margin:15px 0;
        font-weight:bold;
        text-align:center;
    }
    .train-content{
        margin-top:30px;
        font-size:30px;
        color:#333;
        line-height:40px;
    }
}
</style>
