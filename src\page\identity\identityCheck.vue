<template>
    <div class="identityCheck">
        <div class="super-inspect">
					<div class="simg" :style="{backgroundImage:'url('+supPhoto+')', backgroundRepeat:'no-repeat',backgroundPosition: 'center center',backgroundSize: 'cover' }"></div>
					<!-- <img :src="supPhoto" alt=""> -->
				</div>
        <div class="user-name">{{supName}}</div>
        <div class="job-time">工作有效期  {{supJoinedDate}} ~ {{supInvalidDate}}</div>
        <div class="qrcode" @click="upload">
            <canvas id="canvas" class="canvas"></canvas>
            <div class="upload-model" v-show="uploadShow"></div>
        </div>
        <div class="upload" v-if="!uploadShow">点击二维码刷新</div>
        <div class="upload" v-else>已刷新</div>
        <div class="qrcode-txt">社会监督员电子证</div>
        <div class="confirm-btn" @touchstart="confirm">确认身份并开始检查</div>
    </div>
</template>
<script>
import QRCode from 'qrcode'
import { getSupervisorQRcode } from '@/api/identity'
import { getUserIn } from '@/api/sign'
import { initCookie } from '@/api/check'
import userService from '@/services/userService'
export default {
    data () {
        return {
            url: '',
            checkId: '',
            supName: '',
            supPhoto: '',
            supJoinedDate: '',
            supInvalidDate: '',
            uploadShow: false
        }
    },
    mounted() {
        // this.$loading(true, '');
        this.checkId = this.$route.query.checkId
        this.getSupervisorQRcode()
        this.getUserInfo()
    },
    methods: {
        useqrcode () {
            let that = this
            var canvas = document.getElementById('canvas')
            QRCode.toCanvas(canvas, this.url, function (error) {
            if (error) console.error(error)
                console.log('success!')
            })
        },
        getSupervisorQRcode () {
            getSupervisorQRcode().then((res)=>{
                console.log(res)
                this.url = res.data
                let index = this.url.indexOf('=')
                let seesionUrl = this.url.substring(index+1, this.url.length)
                this.useqrcode()
            }).catch((err)=>{

            })
        },
        confirm() {
						console.log(this.checkId)
						const startTime = new Date(this.supJoinedDate).getTime();
						const stopTime = new Date(this.supInvalidDate).getTime();
						const nowTime = new Date().getTime();
						// console.log('startTime==', startTime);
						// console.log('stopTime==', stopTime);
						if (nowTime > startTime && nowTime < stopTime) {
							this.$router.push({
									path: '/check',
									query: {
											checkId: this.checkId
									}
							})
						} else {
							this.$toast.center('工作不在有效期内');
						}
        },
        async getUserInfo() {
            try {
                let info = JSON.parse(localStorage.getItem('userInfo2'));
                if (info) {
                    this.supName = info.userName || info.supName || '';
                    this.supPhoto = info.photo || info.supPhoto || '';
                    this.supJoinedDate = info.joinedDate || info.supJoinedDate || '';
                    this.supInvalidDate = info.invalidDate || info.supInvalidDate || '';
                    this.$loading(false, '');
                } else {
                    this.$toast.center('获取用户信息失败');
                }
            } catch (error) {
                console.error('获取用户信息异常:', error);
                this.$toast.center('获取用户信息异常');
            }
        },
         getInitCookie () {
            let that = this
			window.yl.call('getAuthcode', {}, {
				onSuccess: (res) => {
					console.log(res.param.authCode)
					initCookie({
						authCode: res.param.authCode
					}).then((res)=>{
						console.log(1, res)
						if(res.errorCode === '0') {
                            that.getUserInfo()
                            that.getSupervisorQRcode()
						} else {
						    that.$toast.center(res.value)
						}
					}).catch((e)=>{
						console.log(e)
					})
				},
				onFail: (res) => {

				}
            })
        },
        upload () {
            this.uploadShow = true
            this.getSupervisorQRcode()
            let that = this
            setTimeout(function() {
                that.uploadShow = false
            }, 1000)
        }
    }
}
</script>
<style lang="less" scoped>
.identityCheck{
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}
.super-inspect{
    width: 198px;
    height: 198px;
    margin: 36px auto;
    padding: 30px;
    border-radius: 50%;
    // background:#eee;
    border: 1px solid #4288ff; /*px*/
    overflow: hidden;
    display: flex;
    justify-content: center;
		align-items: center;
		.simg{
      width: 100%;
      height: 100%;
			border-radius: 50%;
		}
}
.user-name{
    text-align:center;
    margin-bottom:37px;
    color:#333;
    font-size:34px;
}
.job-time{
    color:#777777;
    font-size:30px;
    text-align:center;
    padding-bottom:50px;
    border-bottom:1px solid #ececec;
}
.upload{
    text-align:center;
    font-size:24px;
    margin:20px 0;
}
.qrcode-txt{
    font-size:32px;
    margin-top:10px;
    text-align:center;
    margin-bottom:198px;
}
.qrcode{
    width:458px;
    height:456px;
    margin:15px auto 0px auto;
    background:#ddd;
    position: relative;
    .canvas{
        width:458px !important;
        height:458px !important;
    }
    .upload-model {
        width:100%;
        height:100%;
        position:absolute;
        top:0;
        left:0;
    }
}
.confirm-btn{
    position:fixed;
    bottom:40px;
    left:30px;
    width:690px;
    height:98px;
    line-height:98px;
    text-align:center;
    color:#fff;
    font-size:32px;
    background:#317dff;
    border-radius:4px;
    // margin:108px auto 50px auto;
}
</style>
